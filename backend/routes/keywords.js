import express from "express";
import fetch from "node-fetch";
import PluginKeyword from "../models/PluginKeyword.js";
import PluginKeywordRank from "../models/PluginKeywordRank.js";
import PluginInformation from "../models/PluginInformation.js";
import AddedPlugin from "../models/AddedPlugin.js";
import Plugin from "../models/Plugin.js";
import { authenticateToken } from "../middleware/auth.js";

const router = express.Router();

// Helper function to fetch plugin rank from WordPress API
const fetchPluginRank = async (keyword, pluginSlug) => {
  try {
    const searchKeyword = keyword;

    console.log(
      `🔍 Searching for keyword "${searchKeyword}" to find rank of plugin "${pluginSlug}"`
    );

    // Get plugin information from database to help with matching
    let pluginInfo = null;
    try {
      pluginInfo = await Plugin.findBySlug(pluginSlug);
      if (pluginInfo) {
        console.log(
          `📋 Plugin info from DB: name="${pluginInfo.name}", slug="${pluginInfo.slug}"`
        );
      }
    } catch (dbError) {
      console.log(`⚠️ Could not fetch plugin info from DB: ${dbError.message}`);
    }

    // First, try the standard keyword search
    let apiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(
      searchKeyword
    )}&request[per_page]=100`;

    let response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`WordPress API request failed: ${response.status}`);
    }

    let data = await response.json();

    if (!data.plugins || !Array.isArray(data.plugins)) {
      return {
        rank: null,
        totalResults: 0,
        error: "No plugins found in API response",
      };
    }

    console.log(
      `📊 Found ${data.plugins.length} plugins for keyword "${searchKeyword}"`
    );

    // Enhanced plugin matching: try both slug and name matching
    let pluginIndex = data.plugins.findIndex((plugin) => {
      // First try exact slug match
      if (plugin.slug === pluginSlug) {
        return true;
      }

      // If we have plugin info from DB, try name matching
      if (pluginInfo && pluginInfo.name) {
        // Try exact name match
        if (plugin.name === pluginInfo.name) {
          return true;
        }

        // Try case-insensitive name match
        if (plugin.name.toLowerCase() === pluginInfo.name.toLowerCase()) {
          return true;
        }

        // Try partial name match (for cases like "SchedulePress – WordPress Editorial Calendar & Scheduled Blog Posts Plugin")
        if (
          plugin.name.toLowerCase().includes(pluginInfo.name.toLowerCase()) ||
          pluginInfo.name.toLowerCase().includes(plugin.name.toLowerCase())
        ) {
          return true;
        }
      }

      return false;
    });

    if (pluginIndex !== -1) {
      const rank = pluginIndex + 1;
      const foundPlugin = data.plugins[pluginIndex];
      console.log(
        `✅ Plugin "${pluginSlug}" found at position ${rank} for keyword "${searchKeyword}" (matched by: ${
          foundPlugin.slug === pluginSlug ? "slug" : "name"
        }, plugin name: "${foundPlugin.name}")`
      );
      return {
        rank: rank,
        totalResults: data.plugins.length,
        error: null,
      };
    }

    // If not found in first 100 results, try a more specific search for wp-scheduled-posts
    if (pluginSlug === "wp-scheduled-posts") {
      console.log(
        `🔍 Plugin wp-scheduled-posts not found in keyword search, trying SchedulePress search...`
      );

      // Try searching for "SchedulePress" which is the plugin's display name
      const schedulePressUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=SchedulePress&request[per_page]=100`;

      const schedulePressResponse = await fetch(schedulePressUrl);
      if (schedulePressResponse.ok) {
        const schedulePressData = await schedulePressResponse.json();

        if (
          schedulePressData.plugins &&
          Array.isArray(schedulePressData.plugins)
        ) {
          const schedulePressIndex = schedulePressData.plugins.findIndex(
            (plugin) => plugin.slug === "wp-scheduled-posts"
          );

          if (schedulePressIndex !== -1) {
            // Found in SchedulePress search, but we need to estimate its rank for the original keyword
            // Since it wasn't in the keyword search, we'll assign a rank beyond 100
            const estimatedRank = 101 + schedulePressIndex;
            console.log(
              `✅ wp-scheduled-posts found in SchedulePress search at position ${
                schedulePressIndex + 1
              }, estimated rank for "${searchKeyword}": ${estimatedRank}`
            );
            return {
              rank: estimatedRank,
              totalResults: 100,
              error: null,
            };
          }
        }
      }

      // If still not found, try searching through multiple pages for the keyword
      console.log(
        `🔍 Trying extended search for wp-scheduled-posts with keyword "${searchKeyword}"...`
      );

      for (let page = 2; page <= 5; page++) {
        try {
          const extendedUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(
            searchKeyword
          )}&request[page]=${page}&request[per_page]=100`;

          const extendedResponse = await fetch(extendedUrl);
          if (extendedResponse.ok) {
            const extendedData = await extendedResponse.json();

            if (extendedData.plugins && Array.isArray(extendedData.plugins)) {
              // Use the same enhanced matching logic for extended search
              const extendedIndex = extendedData.plugins.findIndex((plugin) => {
                // First try exact slug match
                if (plugin.slug === pluginSlug) {
                  return true;
                }

                // If we have plugin info from DB, try name matching
                if (pluginInfo && pluginInfo.name) {
                  // Try exact name match
                  if (plugin.name === pluginInfo.name) {
                    return true;
                  }

                  // Try case-insensitive name match
                  if (
                    plugin.name.toLowerCase() === pluginInfo.name.toLowerCase()
                  ) {
                    return true;
                  }

                  // Try partial name match
                  if (
                    plugin.name
                      .toLowerCase()
                      .includes(pluginInfo.name.toLowerCase()) ||
                    pluginInfo.name
                      .toLowerCase()
                      .includes(plugin.name.toLowerCase())
                  ) {
                    return true;
                  }
                }

                return false;
              });

              if (extendedIndex !== -1) {
                const rank = (page - 1) * 100 + extendedIndex + 1;
                const foundPlugin = extendedData.plugins[extendedIndex];
                console.log(
                  `✅ Plugin "${pluginSlug}" found on page ${page} at position ${
                    extendedIndex + 1
                  }, rank: ${rank} (matched by: ${
                    foundPlugin.slug === pluginSlug ? "slug" : "name"
                  }, plugin name: "${foundPlugin.name}")`
                );
                return {
                  rank: rank,
                  totalResults: page * 100,
                  error: null,
                };
              }
            }
          }

          // Add delay between requests
          await new Promise((resolve) => setTimeout(resolve, 200));
        } catch (pageError) {
          console.log(`Error searching page ${page}:`, pageError.message);
        }
      }
    }

    console.log(
      `❌ Plugin "${pluginSlug}" not found in search results for keyword "${searchKeyword}"`
    );
    return {
      rank: null,
      totalResults: data.plugins.length,
      error: "Plugin not found in search results",
    };
  } catch (error) {
    console.error("Error fetching plugin rank:", error);
    return { rank: null, totalResults: 0, error: error.message };
  }
};

// Helper function to format date as dd-mm-yyyy
const formatDate = (date = new Date()) => {
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};

// Get keywords for user (optionally filtered by plugin)
router.get("/", authenticateToken, async (req, res) => {
  // Set JSON headers immediately
  res.setHeader("Content-Type", "application/json");

  try {
    const { pluginSlug } = req.query;

    console.log(
      `Getting keywords for plugin: ${
        pluginSlug || "all"
      } from pluginkeywordranks collection (accessible to all authenticated users)`
    );

    // Validate pluginSlug if provided
    if (pluginSlug && typeof pluginSlug !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid plugin slug format",
      });
    }

    // Add timeout protection
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Request timeout")), 25000); // 25 second timeout
    });

    // Import required models
    const { default: PluginKeywordRank } = await import(
      "../models/PluginKeywordRank.js"
    );
    const { default: PluginInformation } = await import(
      "../models/PluginInformation.js"
    );

    // Get ALL plugins from plugininformations collection (same as Plugin Rank page)
    let pluginQuery = {
      isActive: true,
    };
    if (pluginSlug) {
      pluginQuery.pluginSlug = pluginSlug.toLowerCase();
    }

    const allPlugins = await PluginInformation.find(pluginQuery).select(
      "pluginSlug pluginName displayName"
    );

    console.log(
      `Found ${allPlugins.length} plugins from plugininformations collection`
    );

    if (allPlugins.length === 0) {
      console.log(
        "No plugins found in plugininformations collection, returning empty array"
      );
      return res.json({
        success: true,
        keywords: [],
        count: 0,
        message: "No plugins found",
      });
    }

    // Get plugin slugs from all plugins
    const pluginSlugs = allPlugins.map((plugin) => plugin.pluginSlug);
    console.log("Plugin slugs from plugininformations:", pluginSlugs);

    // Get keyword rank records for these plugins
    const keywordsPromise = PluginKeywordRank.find({
      pluginSlug: { $in: pluginSlugs },
      isActive: true,
    }).sort({ pluginSlug: 1, keyword: 1 });

    const keywordRankRecords = await Promise.race([
      keywordsPromise,
      timeoutPromise,
    ]);

    console.log(
      `Found ${keywordRankRecords.length} keyword rank records from pluginkeywordranks collection`
    );

    // Debug logging for SchedulePress
    const schedulePressSlugs = pluginSlugs.filter(
      (slug) => slug === "wp-scheduled-posts"
    );
    if (schedulePressSlugs.length > 0) {
      const schedulePressKeywords = keywordRankRecords.filter(
        (record) => record.pluginSlug === "wp-scheduled-posts"
      );
      console.log(
        `🔍 SchedulePress debug: Found ${schedulePressKeywords.length} keywords for wp-scheduled-posts:`,
        schedulePressKeywords.map((k) => ({
          keyword: k.keyword,
          currentRank: k.currentRank,
          source: k.source,
        }))
      );
    }

    // Transform keyword rank records into the expected format
    const keywords = [];
    for (const record of keywordRankRecords) {
      // Get latest rank data
      let latestRank = null;
      let previousRank = null;
      let rankChange = null;
      let lastChecked = null;

      // Debug logging for SchedulePress keywords
      const isSchedulePress = record.pluginSlug === "wp-scheduled-posts";
      if (isSchedulePress) {
        console.log(
          `🔍 Processing SchedulePress keyword "${record.keyword}":`,
          {
            currentRank: record.currentRank,
            latestDate: record.latestDate,
            hasRankHistory: !!(
              record.rankHistory && typeof record.rankHistory === "object"
            ),
            rankHistoryKeys: record.rankHistory
              ? Object.keys(record.rankHistory)
              : [],
          }
        );
      }

      if (record.rankHistory && typeof record.rankHistory === "object") {
        const rankHistoryArray = record.getRankHistoryArray(2);
        if (isSchedulePress) {
          console.log(
            `📊 SchedulePress "${record.keyword}" rank history array:`,
            rankHistoryArray
          );
        }
        if (rankHistoryArray.length > 0) {
          latestRank = rankHistoryArray[0].rank;
          lastChecked = rankHistoryArray[0].fetchedAt; // Use fetchedAt for "Updated" column
          if (rankHistoryArray.length > 1) {
            previousRank = rankHistoryArray[1].rank;
            rankChange = previousRank - latestRank; // Positive means improvement
          }
        }
      }

      // Fallback to currentRank if no rank history is available
      if (
        latestRank === null &&
        record.currentRank !== null &&
        record.currentRank !== undefined
      ) {
        latestRank = record.currentRank;
        lastChecked = record.latestDate || record.fetchedAt;
        console.log(
          `Using currentRank fallback for ${record.pluginSlug}-${record.keyword}: rank ${latestRank}`
        );
      }

      // Convert lastChecked date string (dd-mm-yyyy) to proper Date object
      let lastCheckedDate = null;
      if (lastChecked) {
        if (typeof lastChecked === "string" && lastChecked.includes("-")) {
          // Convert dd-mm-yyyy to yyyy-mm-dd for proper Date parsing
          const dateParts = lastChecked.split("-");
          if (dateParts.length === 3) {
            const [day, month, year] = dateParts;
            lastCheckedDate = new Date(`${year}-${month}-${day}`);
            // Validate the date
            if (isNaN(lastCheckedDate.getTime())) {
              lastCheckedDate = record.fetchedAt || new Date();
            }
          } else {
            lastCheckedDate = record.fetchedAt || new Date();
          }
        } else if (lastChecked instanceof Date) {
          lastCheckedDate = lastChecked;
        } else {
          lastCheckedDate = record.fetchedAt || new Date();
        }
      } else {
        lastCheckedDate = record.fetchedAt || new Date();
      }

      // Final debug for SchedulePress
      if (isSchedulePress) {
        console.log(`✅ Final SchedulePress "${record.keyword}" data:`, {
          latestRank,
          previousRank,
          rankChange,
          lastChecked,
          lastCheckedDate,
        });
      }

      keywords.push({
        _id: `${record.pluginSlug}-${record.keyword}`,
        keyword: record.keyword,
        pluginSlug: record.pluginSlug,
        pluginName: record.pluginName,
        source: record.source || "default",
        addedAt: record.createdAt || record.fetchedAt,
        updatedAt: record.updatedAt || record.fetchedAt,
        type: "plugin_tags",
        isActive: record.isActive !== false,
        latestRank: latestRank,
        previousRank: previousRank,
        rankChange: rankChange,
        lastChecked: lastCheckedDate,
        occurrences: record.currentOccurrences || 0,
      });
    }

    console.log(
      `Returning ${keywords.length} keywords from pluginkeywordranks collection`
    );

    res.json({
      success: true,
      keywords,
      count: keywords.length,
      pluginSlug: pluginSlug || null,
    });
  } catch (error) {
    console.error("Get keywords error:", error);
    console.error("Error stack:", error.stack);

    // Ensure we can still respond
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: "Failed to get keywords",
        error:
          process.env.NODE_ENV === "production"
            ? "Internal server error"
            : error.message,
        pluginSlug: req.query.pluginSlug || null,
      });
    }
  }
});

// Add new keyword
router.post("/", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const { pluginSlug, pluginName, keyword } = req.body;

    console.log(
      `🔄 Adding keyword "${keyword}" for plugin "${pluginSlug}" (${pluginName}) by user ${userId}`
    );

    // Validate required fields
    if (!pluginSlug || !pluginName || !keyword) {
      return res.status(400).json({
        success: false,
        message: "Plugin slug, plugin name, and keyword are required",
      });
    }

    const newKeyword = await PluginKeyword.addKeywordForUser(userId, {
      pluginSlug,
      pluginName,
      keyword,
    });

    console.log(
      `✅ Keyword "${keyword}" added to PluginKeyword collection for plugin ${pluginSlug}`
    );

    // Calculate keyword occurrences in plugin content
    let occurrences = 0;
    try {
      const pluginInfo = await PluginInformation.findOne({
        pluginSlug: pluginSlug.toLowerCase(),
        isActive: true,
      });

      if (pluginInfo && pluginInfo.sections) {
        const keywordLower = keyword.toLowerCase();
        const sections = pluginInfo.sections;

        // Helper function to count occurrences in text
        const countOccurrences = (text) => {
          if (!text || typeof text !== "string") return 0;
          const textLower = text.toLowerCase();
          const regex = new RegExp(
            keywordLower.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
            "g"
          );
          const matches = textLower.match(regex);
          return matches ? matches.length : 0;
        };

        // Count in all sections
        Object.values(sections).forEach((sectionContent) => {
          if (typeof sectionContent === "string") {
            occurrences += countOccurrences(sectionContent);
          }
        });

        // Note: Occurrences are calculated but not stored in the keyword object
        // to maintain consistency with existing keyword format
      }
    } catch (error) {
      console.error("Error calculating keyword occurrences:", error);
      // Don't fail the entire operation if occurrences calculation fails
    }

    // Fetch plugin rank from WordPress API
    const rankResult = await fetchPluginRank(keyword, pluginSlug);

    // Always save to PluginKeywordRank collection, even if rank fetch failed
    // This ensures the keyword shows up in the main keyword list
    const currentDate = formatDate();
    let rankData = null;
    try {
      const { default: PluginKeywordRank } = await import(
        "../models/PluginKeywordRank.js"
      );

      rankData = await PluginKeywordRank.upsertKeywordRank(
        pluginSlug,
        pluginName,
        keyword,
        rankResult.rank, // This can be null if rank fetch failed
        currentDate,
        "manual", // Keywords added manually have source "manual"
        occurrences
      );

      console.log(
        `✅ Keyword "${keyword}" saved to PluginKeywordRank for plugin ${pluginSlug}: rank ${
          rankResult.rank || "null"
        }`
      );
    } catch (rankError) {
      console.error("Error saving to PluginKeywordRank:", rankError);
      // Don't fail the entire operation if rank saving fails, but log the error
    }

    res.status(201).json({
      success: true,
      message: "Keyword added successfully",
      keyword: newKeyword,
      rank: rankResult.rank,
      rankData: rankData,
      rankError: rankResult.error,
      occurrences: occurrences,
    });
  } catch (error) {
    console.error("Add keyword error:", error);

    if (error.message.includes("already exists")) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to add keyword",
      error: error.message,
    });
  }
});

// Bulk delete keywords (must come before /:keywordId route)
router.delete("/bulk-delete", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const { keywordIds } = req.body;

    if (!keywordIds || !Array.isArray(keywordIds) || keywordIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Keyword IDs array is required",
      });
    }

    // Import PluginKeywordRank model
    const { default: PluginKeywordRank } = await import(
      "../models/PluginKeywordRank.js"
    );

    let deletedCount = 0;
    const errors = [];

    // Process each keyword ID
    for (const keywordId of keywordIds) {
      try {
        const result = await PluginKeywordRank.removeKeywordForUser(
          userId,
          keywordId
        );
        if (result) {
          deletedCount++;
        }
      } catch (error) {
        console.error(`Error deleting keyword ${keywordId}:`, error);
        errors.push({
          keywordId,
          error: error.message,
        });
      }
    }

    if (deletedCount === 0) {
      return res.status(404).json({
        success: false,
        message: "No keywords found to delete",
        errors,
      });
    }

    res.json({
      success: true,
      message: `${deletedCount} keywords deleted successfully`,
      deletedCount,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Bulk delete keywords error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete keywords",
      error: error.message,
    });
  }
});

// Delete keyword
router.delete("/:keywordId", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const { keywordId } = req.params;

    // Import PluginKeywordRank model
    const { default: PluginKeywordRank } = await import(
      "../models/PluginKeywordRank.js"
    );

    const result = await PluginKeywordRank.removeKeywordForUser(
      userId,
      keywordId
    );

    if (!result) {
      return res.status(404).json({
        success: false,
        message: "Keyword not found",
      });
    }

    res.json({
      success: true,
      message: "Keyword deleted successfully",
    });
  } catch (error) {
    console.error("Delete keyword error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete keyword",
      error: error.message,
    });
  }
});

// Get keywords for specific plugin - Now fetches from pluginkeywordranks collection
router.get("/plugin/:pluginSlug", authenticateToken, async (req, res) => {
  try {
    const { pluginSlug } = req.params;

    console.log(
      `Getting keywords for plugin ${pluginSlug} from pluginkeywordranks collection (accessible to all authenticated users)`
    );

    // Ensure proper JSON response headers
    res.setHeader("Content-Type", "application/json");

    // Import required models
    const { default: PluginKeywordRank } = await import(
      "../models/PluginKeywordRank.js"
    );
    const { default: AddedPlugin } = await import("../models/AddedPlugin.js");
    const { default: User } = await import("../models/User.js");

    // Get plugins added by admin/superadmin users (accessible to all authenticated users)
    const adminUsers = await User.find({
      role: { $in: ["admin", "superadmin"] },
      isActive: true,
    }).select("_id");

    const adminUserIds = adminUsers.map((user) => user._id);

    // Check if this plugin is added by admin/superadmin users
    const addedPlugin = await AddedPlugin.findOne({
      userId: { $in: adminUserIds },
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    });

    if (!addedPlugin) {
      return res.json({
        success: true,
        keywords: [],
        count: 0,
        pluginSlug,
        message: "Plugin not found in added plugins",
      });
    }

    // Get keyword rank records for this plugin
    const keywordRankRecords = await PluginKeywordRank.find({
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    }).sort({ keyword: 1 });

    // Transform keyword rank records into the expected format
    const keywords = [];
    for (const record of keywordRankRecords) {
      // Get latest rank data
      let latestRank = null;
      let previousRank = null;
      let rankChange = null;
      let lastChecked = null;

      if (record.rankHistory && typeof record.rankHistory === "object") {
        const rankHistoryArray = record.getRankHistoryArray(2);
        if (rankHistoryArray.length > 0) {
          latestRank = rankHistoryArray[0].rank;
          lastChecked = rankHistoryArray[0].fetchedAt; // Use fetchedAt for "Updated" column
          if (rankHistoryArray.length > 1) {
            previousRank = rankHistoryArray[1].rank;
            rankChange = previousRank - latestRank; // Positive means improvement
          }
        }
      }

      keywords.push({
        _id: `${record.pluginSlug}-${record.keyword}`,
        keyword: record.keyword,
        pluginSlug: record.pluginSlug,
        pluginName: record.pluginName,
        source: record.source || "default",
        addedAt: record.createdAt || record.fetchedAt,
        updatedAt: record.updatedAt || record.fetchedAt,
        type: "plugin_tags",
        isActive: record.isActive !== false,
        latestRank: latestRank,
        previousRank: previousRank,
        rankChange: rankChange,
        lastChecked: lastChecked,
        occurrences: record.currentOccurrences || 0,
      });
    }

    console.log(
      `Found ${keywords.length} keywords for plugin ${pluginSlug} from pluginkeywordranks collection`
    );

    res.json({
      success: true,
      keywords,
      count: keywords.length,
      pluginSlug,
    });
  } catch (error) {
    console.error("Get plugin keywords error:", error);
    console.error("Error stack:", error.stack);

    // Ensure proper JSON response headers
    res.setHeader("Content-Type", "application/json");

    res.status(500).json({
      success: false,
      message: "Failed to get plugin keywords",
      error:
        process.env.NODE_ENV === "production"
          ? "Internal server error"
          : error.message,
    });
  }
});

// Update keyword (for future use)
router.put("/:keywordId", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const { keywordId } = req.params;
    const { keyword, searchVolume, difficulty, position } = req.body;

    const updatedKeyword = await PluginKeyword.findOneAndUpdate(
      { _id: keywordId, userId },
      {
        ...(keyword && { keyword: keyword.toLowerCase().trim() }),
        ...(searchVolume !== undefined && { searchVolume }),
        ...(difficulty !== undefined && { difficulty }),
        ...(position !== undefined && { position }),
        lastChecked: new Date(),
      },
      { new: true }
    );

    if (!updatedKeyword) {
      return res.status(404).json({
        success: false,
        message: "Keyword not found",
      });
    }

    res.json({
      success: true,
      message: "Keyword updated successfully",
      keyword: updatedKeyword,
    });
  } catch (error) {
    console.error("Update keyword error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update keyword",
      error: error.message,
    });
  }
});

// Get keyword rank history
router.get(
  "/ranks/:keyword/:pluginSlug",
  authenticateToken,
  async (req, res) => {
    try {
      const { keyword, pluginSlug } = req.params;
      const { limit = 30 } = req.query;

      const rankHistory = await PluginKeyword.getKeywordRankHistory(
        pluginSlug,
        keyword,
        parseInt(limit)
      );

      res.json({
        success: true,
        rankHistory,
        count: rankHistory.length,
        keyword,
        pluginSlug,
      });
    } catch (error) {
      console.error("Get keyword rank history error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get keyword rank history",
        error: error.message,
      });
    }
  }
);

// Refresh all keyword ranks
router.post("/refresh-ranks", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    console.log(`🔄 Manual keyword refresh triggered by user ${userId}`);

    // Import required models
    const { default: PluginKeywordRank } = await import(
      "../models/PluginKeywordRank.js"
    );
    const { default: PluginKeyword } = await import(
      "../models/PluginKeyword.js"
    );
    const { default: PluginInformation } = await import(
      "../models/PluginInformation.js"
    );

    // Get ALL plugins from pluginkeywords collection (this is the source of truth for keywords)
    const pluginKeywordDocs = await PluginKeyword.find({
      isActive: true,
    }).select("pluginSlug pluginName keywords");

    if (pluginKeywordDocs.length === 0) {
      return res.json({
        success: true,
        message:
          "No plugins found in pluginkeywords collection to refresh keywords for",
        refreshedCount: 0,
      });
    }

    console.log(
      `Found ${pluginKeywordDocs.length} plugins from pluginkeywords collection to refresh keywords for`
    );

    // Extract all keyword entries from all plugins
    let allKeywordEntries = [];

    for (const pluginDoc of pluginKeywordDocs) {
      if (pluginDoc.keywords && typeof pluginDoc.keywords === "object") {
        // Convert keywords object to array of entries
        Object.keys(pluginDoc.keywords).forEach((keywordKey) => {
          const keywordData = pluginDoc.keywords[keywordKey];
          if (keywordData && keywordData.keyword) {
            allKeywordEntries.push({
              pluginSlug: pluginDoc.pluginSlug,
              pluginName: pluginDoc.pluginName,
              keyword: keywordData.keyword,
              source: keywordData.source || "default",
              keywordKey: keywordKey,
            });
          }
        });
      }
    }

    if (allKeywordEntries.length === 0) {
      return res.json({
        success: true,
        message: "No keywords found to refresh",
        refreshedCount: 0,
      });
    }

    console.log(`Found ${allKeywordEntries.length} keywords to refresh`);

    const currentDate = formatDate();
    let refreshedCount = 0;
    let errors = [];

    // Helper function to calculate keyword occurrences
    const calculateKeywordOccurrences = async (keyword, pluginSlug) => {
      try {
        const pluginInfo = await PluginInformation.findOne({
          pluginSlug: pluginSlug.toLowerCase(),
          isActive: true,
        });

        if (!pluginInfo || !pluginInfo.pluginInfo) {
          return 0;
        }

        const content = JSON.stringify(pluginInfo.pluginInfo).toLowerCase();
        const keywordLower = keyword.toLowerCase();
        const regex = new RegExp(keywordLower, "gi");
        const matches = content.match(regex);
        return matches ? matches.length : 0;
      } catch (error) {
        console.error(`Error calculating occurrences for ${keyword}:`, error);
        return 0;
      }
    };

    // Process each keyword entry from pluginkeywords collection
    for (const keywordEntry of allKeywordEntries) {
      try {
        console.log(
          `🔍 Refreshing rank for keyword "${keywordEntry.keyword}" in plugin ${keywordEntry.pluginSlug}`
        );

        const rankResult = await fetchPluginRank(
          keywordEntry.keyword,
          keywordEntry.pluginSlug
        );

        if (rankResult.rank !== null) {
          // Calculate keyword occurrences
          const occurrences = await calculateKeywordOccurrences(
            keywordEntry.keyword,
            keywordEntry.pluginSlug
          );

          // Update keyword rank using PluginKeywordRank model (upsert to avoid duplicates)
          await PluginKeywordRank.upsertKeywordRank(
            keywordEntry.pluginSlug,
            keywordEntry.pluginName,
            keywordEntry.keyword,
            rankResult.rank,
            currentDate,
            keywordEntry.source,
            occurrences
          );

          refreshedCount++;
          console.log(
            `✅ Updated rank for "${keywordEntry.keyword}" in ${keywordEntry.pluginSlug}: rank ${rankResult.rank}, occurrences ${occurrences}`
          );
        } else {
          // Even if rank fetch failed, we should still create/update the record with null rank
          // This ensures the keyword shows up in the keyword performance table
          const occurrences = await calculateKeywordOccurrences(
            keywordEntry.keyword,
            keywordEntry.pluginSlug
          );

          await PluginKeywordRank.upsertKeywordRank(
            keywordEntry.pluginSlug,
            keywordEntry.pluginName,
            keywordEntry.keyword,
            null, // rank is null when fetch failed
            currentDate,
            keywordEntry.source,
            occurrences
          );

          errors.push({
            keyword: keywordEntry.keyword,
            pluginSlug: keywordEntry.pluginSlug,
            error: rankResult.error,
          });
          console.log(
            `❌ Failed to get rank for "${keywordEntry.keyword}" in ${keywordEntry.pluginSlug}: ${rankResult.error} (but created/updated record with null rank)`
          );
        }

        // Add small delay between requests to be respectful to the API
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        console.error(
          `Error refreshing rank for keyword ${keywordEntry.keyword}:`,
          error
        );
        errors.push({
          keyword: keywordEntry.keyword,
          pluginSlug: keywordEntry.pluginSlug,
          error: error.message,
        });
      }
    }

    console.log(
      `✅ Manual keyword refresh completed: ${refreshedCount} keywords updated, ${errors.length} errors`
    );

    res.json({
      success: true,
      message: `Refreshed ranks for ${refreshedCount} keywords`,
      refreshedCount,
      totalKeywords: allKeywordEntries.length,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Refresh keyword ranks error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to refresh keyword ranks",
      error: error.message,
    });
  }
});

// Get keyword occurrences in plugin content
router.get(
  "/occurrences/:pluginSlug/:keyword",
  authenticateToken,
  async (req, res) => {
    try {
      const { pluginSlug, keyword } = req.params;

      // Import required models
      const { default: AddedPlugin } = await import("../models/AddedPlugin.js");
      const { default: User } = await import("../models/User.js");

      // Check if this plugin is added by admin/superadmin users (accessible to all authenticated users)
      const adminUsers = await User.find({
        role: { $in: ["admin", "superadmin"] },
        isActive: true,
      }).select("_id");

      const adminUserIds = adminUsers.map((user) => user._id);

      const addedPlugin = await AddedPlugin.findOne({
        userId: { $in: adminUserIds },
        pluginSlug: pluginSlug.toLowerCase(),
        isActive: true,
      });

      if (!addedPlugin) {
        return res.status(404).json({
          success: false,
          message: "Plugin not found in added plugins",
        });
      }

      // Get plugin information
      const pluginInfo = await PluginInformation.findOne({
        pluginSlug: pluginSlug.toLowerCase(),
        isActive: true,
      });

      if (!pluginInfo || !pluginInfo.sections) {
        return res.json({
          success: true,
          occurrences: 0,
          message: "No plugin content found",
        });
      }

      // Count keyword occurrences in all sections (case-insensitive)
      let totalOccurrences = 0;
      const keywordLower = keyword.toLowerCase();
      const sections = pluginInfo.sections;

      // Helper function to count occurrences in text
      const countOccurrences = (text) => {
        if (!text || typeof text !== "string") return 0;
        const textLower = text.toLowerCase();
        const regex = new RegExp(
          keywordLower.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
          "g"
        );
        const matches = textLower.match(regex);
        return matches ? matches.length : 0;
      };

      // Count in all sections
      Object.values(sections).forEach((sectionContent) => {
        if (typeof sectionContent === "string") {
          totalOccurrences += countOccurrences(sectionContent);
        }
      });

      res.json({
        success: true,
        occurrences: totalOccurrences,
        pluginSlug,
        keyword,
      });
    } catch (error) {
      console.error("Get keyword occurrences error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get keyword occurrences",
        error: error.message,
      });
    }
  }
);

// Get related plugins containing the same keyword
router.get("/related-plugins/:keyword", authenticateToken, async (req, res) => {
  try {
    const { keyword } = req.params;
    const { limit = 10 } = req.query;

    // Search for plugins containing the keyword in their content
    const keywordLower = keyword.toLowerCase();
    const keywordRegex = new RegExp(
      keywordLower.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
      "i"
    );

    // Find plugins that contain the keyword in their sections
    const relatedPlugins = await PluginInformation.find({
      isActive: true,
      $or: [
        { "sections.description": keywordRegex },
        { "sections.installation": keywordRegex },
        { "sections.faq": keywordRegex },
        { "sections.changelog": keywordRegex },
        { "sections.screenshots": keywordRegex },
        { "pluginInfo.short_description": keywordRegex },
        { "pluginInfo.name": keywordRegex },
      ],
    })
      .select(
        "pluginSlug pluginName pluginInfo.active_installs rating numRatings tested lastUpdated"
      )
      .limit(parseInt(limit))
      .sort({ "pluginInfo.active_installs": -1 }); // Sort by active installs descending

    // Format the response
    const formattedPlugins = relatedPlugins.map((plugin) => ({
      pluginSlug: plugin.pluginSlug,
      pluginName: plugin.pluginName,
      activeInstalls: plugin.pluginInfo?.active_installs || 0,
      rating: plugin.rating || 0,
      numRatings: plugin.numRatings || 0,
      tested: plugin.tested || "N/A",
      lastUpdated: plugin.lastUpdated || "N/A",
      wordpressUrl: `https://wordpress.org/plugins/${plugin.pluginSlug}/`,
    }));

    res.json({
      success: true,
      relatedPlugins: formattedPlugins,
      keyword,
      count: formattedPlugins.length,
    });
  } catch (error) {
    console.error("Get related plugins error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get related plugins",
      error: error.message,
    });
  }
});

// Debug endpoint to check database state
router.get("/debug", authenticateToken, async (req, res) => {
  res.setHeader("Content-Type", "application/json");

  try {
    const userId = req.user._id;

    // Check user's added plugins
    const addedPlugins = await AddedPlugin.find({
      userId,
      isActive: true,
    })
      .select("pluginSlug pluginName")
      .lean();

    // Check total keywords documents
    const totalKeywordDocs = await PluginKeyword.countDocuments({
      isActive: true,
    });

    // Check keywords for user's plugins
    const pluginSlugs = addedPlugins.map((p) => p.pluginSlug);
    const userKeywordDocs = await PluginKeyword.find({
      pluginSlug: { $in: pluginSlugs },
      isActive: true,
    })
      .select("pluginSlug keywords")
      .lean();

    res.json({
      success: true,
      userId: userId.toString(),
      addedPluginsCount: addedPlugins.length,
      addedPlugins,
      totalKeywordDocsInDb: totalKeywordDocs,
      userKeywordDocsCount: userKeywordDocs.length,
      userKeywordDocs: userKeywordDocs.map((doc) => ({
        pluginSlug: doc.pluginSlug,
        keywordCount: doc.keywords ? Object.keys(doc.keywords).length : 0,
      })),
    });
  } catch (error) {
    console.error("Debug keywords error:", error);
    res.status(500).json({
      success: false,
      message: "Debug failed",
      error: error.message,
    });
  }
});

// Test endpoint to check keywords data
router.get("/test/:pluginSlug", authenticateToken, async (req, res) => {
  res.setHeader("Content-Type", "application/json");

  try {
    const { pluginSlug } = req.params;
    const userId = req.user._id;

    console.log(`Testing keywords for plugin: ${pluginSlug}, user: ${userId}`);

    // Check if user has added this plugin
    const addedPlugin = await AddedPlugin.findOne({
      userId,
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    });

    if (!addedPlugin) {
      return res.json({
        success: false,
        message: "Plugin not found in user's added plugins",
        pluginSlug,
        userId: userId.toString(),
      });
    }

    // Check if keywords exist for this plugin
    const keywordDoc = await PluginKeyword.findOne({
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    });

    if (!keywordDoc) {
      return res.json({
        success: false,
        message: "No keywords document found for this plugin",
        pluginSlug,
        hasAddedPlugin: true,
      });
    }

    const keywordCount = keywordDoc.keywords
      ? Object.keys(keywordDoc.keywords).length
      : 0;

    res.json({
      success: true,
      message: "Keywords data found",
      pluginSlug,
      hasAddedPlugin: true,
      hasKeywordsDoc: true,
      keywordCount,
      keywords: keywordDoc.keywords,
    });
  } catch (error) {
    console.error("Test keywords error:", error);
    res.status(500).json({
      success: false,
      message: "Test failed",
      error: error.message,
    });
  }
});

// Enhanced debug endpoint for keywords troubleshooting
router.get(
  "/debug-detailed/:pluginSlug",
  authenticateToken,
  async (req, res) => {
    res.setHeader("Content-Type", "application/json");

    try {
      const { pluginSlug } = req.params;
      const userId = req.user._id;

      console.log(`Detailed debug for plugin: ${pluginSlug}, user: ${userId}`);

      // 1. Check if user exists and has added plugins
      const userAddedPlugins = await AddedPlugin.find({
        userId,
        isActive: true,
      })
        .select("pluginSlug pluginName")
        .lean();

      // 2. Check if the specific plugin is added by user
      const specificAddedPlugin = await AddedPlugin.findOne({
        userId,
        pluginSlug: pluginSlug.toLowerCase(),
        isActive: true,
      }).lean();

      // 3. Check if keywords document exists for this plugin (regardless of user)
      const keywordDoc = await PluginKeyword.findOne({
        pluginSlug: pluginSlug.toLowerCase(),
        isActive: true,
      }).lean();

      // 4. Check all keyword documents in the database
      const allKeywordDocs = await PluginKeyword.find({
        isActive: true,
      })
        .select("pluginSlug pluginName")
        .lean();

      // 5. Try to run getUserKeywords for this specific plugin
      let userKeywordsResult = null;
      let userKeywordsError = null;
      try {
        userKeywordsResult = await PluginKeyword.getUserKeywords(
          userId,
          pluginSlug
        );
      } catch (error) {
        userKeywordsError = error.message;
      }

      res.json({
        success: true,
        debug: {
          userId: userId.toString(),
          pluginSlug,
          userAddedPluginsCount: userAddedPlugins.length,
          userAddedPlugins: userAddedPlugins.map((p) => p.pluginSlug),
          hasSpecificPluginAdded: !!specificAddedPlugin,
          specificAddedPlugin,
          hasKeywordDoc: !!keywordDoc,
          keywordDocInfo: keywordDoc
            ? {
                pluginSlug: keywordDoc.pluginSlug,
                pluginName: keywordDoc.pluginName,
                keywordCount: keywordDoc.keywords
                  ? Object.keys(keywordDoc.keywords).length
                  : 0,
                fetchedAt: keywordDoc.fetchedAt,
              }
            : null,
          totalKeywordDocsInDb: allKeywordDocs.length,
          allKeywordDocSlugs: allKeywordDocs.map((doc) => doc.pluginSlug),
          userKeywordsResult: userKeywordsResult
            ? {
                count: userKeywordsResult.length,
                keywords: userKeywordsResult.map((k) => ({
                  keyword: k.keyword,
                  pluginSlug: k.pluginSlug,
                  source: k.source,
                })),
              }
            : null,
          userKeywordsError,
        },
      });
    } catch (error) {
      console.error("Detailed debug error:", error);
      res.status(500).json({
        success: false,
        message: "Debug failed",
        error: error.message,
      });
    }
  }
);

export default router;
