import{r,a as ws,b as Xe}from"./vendor-280e31ee.js";import{N as os,u as Ve,a as Ns,L as vs,O as ks,b as Ss,B as Ps,R as As,c as ie}from"./router-208768c5.js";import{C as Cs,X as Ds,A as Rs,I as $s,M as Ts,U as De,S as Ze,L as Es,a as qe,b as Ls,T as we,c as fe,B as is,d as Re,e as cs,f as _s,E as Us,g as Fs,h as Bs,F as ts,i as ze,H as as,D as me,j as Is,k as Ms,P as Se,R as se,l as ds,m as $e,n as Os,o as Hs,p as zs,q as Ke,r as Qe,s as Ks,t as Vs,u as ms,K as qs,v as Ws,w as Oe,x as us,y as Js,z as Ys}from"./icons-9d7a79a3.js";import{R as Pe,B as xs,C as Te,X as Ee,Y as Le,T as Ae,a as hs,L as _e,b as es,c as ss,P as Gs,d as Qs,e as Xs,S as rs,f as Zs}from"./charts-2d8bc326.js";(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))b(o);new MutationObserver(o=>{for(const d of o)if(d.type==="childList")for(const c of d.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&b(c)}).observe(document,{childList:!0,subtree:!0});function a(o){const d={};return o.integrity&&(d.integrity=o.integrity),o.referrerPolicy&&(d.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?d.credentials="include":o.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function b(o){if(o.ep)return;o.ep=!0;const d=a(o);fetch(o.href,d)}})();var gs={exports:{}},We={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var et=r,st=Symbol.for("react.element"),tt=Symbol.for("react.fragment"),at=Object.prototype.hasOwnProperty,rt=et.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,nt={key:!0,ref:!0,__self:!0,__source:!0};function ps(s,l,a){var b,o={},d=null,c=null;a!==void 0&&(d=""+a),l.key!==void 0&&(d=""+l.key),l.ref!==void 0&&(c=l.ref);for(b in l)at.call(l,b)&&!nt.hasOwnProperty(b)&&(o[b]=l[b]);if(s&&s.defaultProps)for(b in l=s.defaultProps,l)o[b]===void 0&&(o[b]=l[b]);return{$$typeof:st,type:s,key:d,ref:c,props:o,_owner:rt.current}}We.Fragment=tt;We.jsx=ps;We.jsxs=ps;gs.exports=We;var e=gs.exports,fs,ns=ws;fs=ns.createRoot,ns.hydrateRoot;const ys=()=>window.location.origin,He=async(s,l={})=>{const a=localStorage.getItem("adminToken"),b=ys(),o={headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}}},d={...o,...l,headers:{...o.headers,...l.headers}},c=await fetch(`${b}${s}`,d);if(c.status===401){let w="Authentication failed. Please login again.";try{const $=await c.json();$.message&&$.message.includes("expired")?w="Your session has expired. Please login again.":$.message&&$.message.includes("token")&&(w="Invalid session. Please login again.")}catch{}throw window.dispatchEvent(new CustomEvent("auth-error",{detail:{message:w}})),new Error(w)}return c},bs=r.createContext(),ye=()=>{const s=r.useContext(bs);if(!s)throw new Error("useAuth must be used within an AuthProvider");return s},lt=({children:s})=>{const[l,a]=r.useState(!1),[b,o]=r.useState(null),[d,c]=r.useState(!0),[w,$]=r.useState(null),N=(v="Session expired. Please login again.")=>{console.warn("Auto-logout triggered:",v),window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),a(!1),o(null),window.toast&&window.toast(v,"warning"),window.location.href="/login"};r.useEffect(()=>{const v=localStorage.getItem("adminToken"),f=localStorage.getItem("adminUser"),i=localStorage.getItem("redirectAfterLogin");v&&f&&(a(!0),o(JSON.parse(f))),i&&$(i),c(!1);const j=R=>{const{message:P}=R.detail;N(P)};return window.addEventListener("auth-error",j),()=>{window.removeEventListener("auth-error",j)}},[]);const S={isAuthenticated:l,user:b,login:async(v,f)=>{try{const i=ys();console.log(`[AUTH] ${new Date().toISOString()} - Attempting login to:`,`${i}/api/auth/login`),console.log(`[AUTH] Login attempt for email: ${v?v.substring(0,3)+"***":"undefined"}`);const j=new AbortController,R=setTimeout(()=>j.abort(),3e4),P=await fetch(`${i}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:v,password:f}),signal:j.signal});clearTimeout(R),console.log(`[AUTH] Response status: ${P.status}`),console.log("[AUTH] Response headers:",Object.fromEntries(P.headers.entries()));const F=await P.text();console.log("[AUTH] Response text (first 200 chars):",F.substring(0,200));let T;try{T=JSON.parse(F),console.log("[AUTH] Parsed response data:",{success:T.success,message:T.message})}catch(D){console.error("[AUTH] Failed to parse response as JSON:",D),console.error("[AUTH] Raw response text:",F);let A="Server returned invalid response. Please try again.";return F.includes("Internal Server Error")?A="Server is experiencing issues. Please try again in a few moments.":F.includes("timeout")?A="Request timed out. Please check your connection and try again.":F.includes("Database")?A="Database connection issue. Please try again.":P.status>=500?A="Server error occurred. Please try again later.":P.status===404&&(A="Login service not found. Please contact support."),{success:!1,message:A}}if(P.ok&&T.success){console.log("[AUTH] Login successful, storing user data"),localStorage.setItem("adminToken",T.token),localStorage.setItem("adminUser",JSON.stringify(T.user)),a(!0),o(T.user);const D=localStorage.getItem("redirectAfterLogin");return D?(localStorage.removeItem("redirectAfterLogin"),$(null),{success:!0,redirectTo:D}):{success:!0}}else return console.log(`[AUTH] Login failed: ${T.message||"Unknown error"}`),{success:!1,message:T.message||"Login failed. Please check your credentials."}}catch(i){console.error("[AUTH] Login error:",{name:i.name,message:i.message,stack:i.stack});let j="Login failed. Please try again.";return i.name==="AbortError"?j="Request timed out. Please check your connection and try again.":i.message.includes("fetch")?j="Network error. Please check your connection and try again.":i.message.includes("NetworkError")&&(j="Network connection failed. Please try again."),{success:!1,message:j}}},logout:(v=!1)=>{v&&window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),a(!1),o(null)},autoLogout:N,loading:d,redirectPath:w};return e.jsx(bs.Provider,{value:S,children:s})},ot=({message:s,type:l="info",duration:a=3e3,onClose:b})=>{const[o,d]=r.useState(!0);r.useEffect(()=>{const N=setTimeout(()=>{d(!1),setTimeout(b,300)},a);return()=>clearTimeout(N)},[a,b]);const c={success:Cs,error:Ds,warning:Rs,info:$s},w={success:"bg-green-50 text-green-800 border-green-200",error:"bg-red-50 text-red-800 border-red-200",warning:"bg-yellow-50 text-yellow-800 border-yellow-200",info:"bg-blue-50 text-blue-800 border-blue-200"},$=c[l];return e.jsx("div",{className:`fixed top-4 right-4 z-50 transition-all duration-300 ${o?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"}`,children:e.jsxs("div",{className:`flex items-center p-4 rounded-lg border shadow-lg ${w[l]}`,children:[e.jsx($,{className:"h-5 w-5 mr-3 flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:s})]})})},it=({children:s})=>{const[l,a]=r.useState([]),b=(d,c="info",w=3e3)=>{const $=Date.now();a(N=>[...N,{id:$,message:d,type:c,duration:w}])},o=d=>{a(c=>c.filter(w=>w.id!==d))};return Xe.useEffect(()=>{window.toast=b},[]),e.jsxs(e.Fragment,{children:[s,l.map(d=>e.jsx(ot,{message:d.message,type:d.type,duration:d.duration,onClose:()=>o(d.id)},d.id))]})},ct=({children:s})=>{const{isAuthenticated:l,loading:a}=ye();return a?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):l?s:e.jsx(os,{to:"/login",replace:!0})},dt=()=>{const[s,l]=r.useState(!1),a=r.useRef(null),{logout:b,user:o}=ye(),d=Ve();r.useEffect(()=>{const N=y=>{a.current&&!a.current.contains(y.target)&&l(!1)};return document.addEventListener("mousedown",N),()=>{document.removeEventListener("mousedown",N)}},[]);const c=()=>{b(),d("/login"),l(!1)},w=N=>{d(N),l(!1)},$=()=>{const N=[{label:"Profile",icon:De,onClick:()=>w("/profile")}];return((o==null?void 0:o.role)==="admin"||(o==null?void 0:o.role)==="superadmin")&&N.push({label:"Settings",icon:Ze,onClick:()=>w("/settings")}),N.push({label:"Logout",icon:Es,onClick:c,className:"text-red-600 hover:bg-red-50"}),N};return e.jsxs("div",{className:"relative",ref:a,children:[e.jsx("button",{onClick:()=>l(!s),className:"flex items-center p-2 text-gray-600 hover:bg-[#edf1f7] hover:text-gray-800 rounded-md transition-colors",title:"More options",children:e.jsx(Ts,{className:"h-4 w-4"})}),s&&e.jsx("div",{className:"absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:$().map((N,y)=>{const U=N.icon;return e.jsxs("button",{onClick:N.onClick,className:`w-full flex items-center px-4 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${N.className||"text-gray-700"}`,children:[e.jsx(U,{className:"h-4 w-4 mr-3"}),N.label]},y)})})]})},mt=s=>{let l=0;if(s.length===0)return l.toString();for(let a=0;a<s.length;a++){const b=s.charCodeAt(a);l=(l<<5)-l+b,l=l&l}return Math.abs(l).toString(16)},ut=(s,l=32)=>`https://www.gravatar.com/avatar/${mt(s)}?s=${l}&d=identicon`,xt=()=>{const{user:s}=ye(),l=Ns(),[a,b]=r.useState(!1),o=()=>{const d=[{name:"Dashboard",path:"/dashboard",icon:Ls},{name:"Plugin Rank",path:"/plugin-rank",icon:we},{name:"Keyword Analysis",path:"/keyword-analysis",icon:fe},{name:"Plugin Data Analysis",path:"/analytics",icon:is}];return((s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="superadmin")&&d.push({name:"Team Members",path:"/users",icon:Re}),d};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>b(!1)}),e.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${a?"translate-x-0":"-translate-x-full"}`,children:[e.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b",children:[e.jsx("img",{src:"/wpdev.png",className:"",alt:"WPDeveloper Logo"}),e.jsx("button",{onClick:()=>b(!1),className:"lg:hidden p-2 rounded-md hover:bg-gray-100",children:e.jsx(qe,{className:"h-5 w-5"})})]}),e.jsx("nav",{className:"mt-6",children:o().map(d=>{const c=d.icon,w=l.pathname===d.path;return e.jsxs(vs,{to:d.path,onClick:()=>b(!1),className:`flex items-center px-6 py-3 text-sm font-medium transition-colors ${w?"bg-blue-50 text-blue-600 border-r-2 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[e.jsx(c,{className:"h-5 w-5 mr-3"}),d.name]},d.path)})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full overflow-hidden",children:e.jsx("img",{src:(s==null?void 0:s.profileImage)||ut((s==null?void 0:s.email)||""),alt:"Profile",className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:(s==null?void 0:s.name)||"Admin User"}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:(s==null?void 0:s.role)||"member"})]})]}),e.jsx(dt,{})]})})]}),e.jsx("div",{className:"lg:pl-64",children:e.jsx("main",{className:"p-6",children:e.jsx(ks,{})})})]})},ht=()=>{const[s,l]=r.useState(""),[a,b]=r.useState(""),[o,d]=r.useState(!1),[c,w]=r.useState(!1),[$,N]=r.useState(""),{login:y}=ye(),U=Ve(),S=async v=>{v.preventDefault(),N(""),w(!0);const f=await y(s,a);if(f.success){const i=f.redirectTo||"/dashboard";U(i)}else N(f.message||"Login failed");w(!1)};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-xl p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center mb-4",children:e.jsx("img",{src:"/wpdev_logo.jpeg",className:"h-16 w-16 rounded-full border"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome Back"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your admin account"})]}),e.jsxs("form",{onSubmit:S,className:"space-y-6",children:[$&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:$}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx(cs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"email",type:"email",value:s,onChange:v=>l(v.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(_s,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"password",type:o?"text":"password",value:a,onChange:v=>b(v.target.value),className:"block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0}),e.jsx("button",{type:"button",onClick:()=>d(!o),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:o?e.jsx(Us,{className:"h-5 w-5"}):e.jsx(Fs,{className:"h-5 w-5"})})]})]}),e.jsx("button",{type:"submit",disabled:c,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:c?"Signing in...":"Sign In"})]})]})})})},he=({isOpen:s,onClose:l,title:a,children:b,maxWidth:o="max-w-xl",fixedHeight:d=!1})=>(r.useEffect(()=>{const c=w=>{w.key==="Escape"&&l()};return s&&(document.addEventListener("keydown",c),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",c),document.body.style.overflow="unset"}},[s,l]),s?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:l}),e.jsxs("div",{className:`relative bg-white rounded-lg shadow-xl ${o} w-full mx-4 transform transition-all ${d?"h-[90vh] flex flex-col":""}`,children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:a}),e.jsx("button",{onClick:l,className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(qe,{className:"h-5 w-5 text-gray-500"})})]}),e.jsx("div",{className:`p-6 ${d?"flex-1 overflow-y-auto":""}`,children:b})]})]})}):null),gt=({isOpen:s,onClose:l,plugin:a})=>{const b=Ve(),o=r.useRef(null),[d,c]=r.useState([]),[w,$]=r.useState([]),[N,y]=r.useState(0),[U,S]=r.useState(0),[v,f]=r.useState([]),[i,j]=r.useState({}),[R,P]=r.useState(null),[F,T]=r.useState({}),[D,A]=r.useState(""),[g,p]=r.useState(""),[k,C]=r.useState(!1),[L,E]=r.useState(!0),[O,B]=r.useState(!0),[K,Q]=r.useState(!0),[Y,X]=r.useState(!0),[re,le]=r.useState(0),ne=(t,h)=>{le(h)},be=async()=>{if(a)try{E(!0);const t=localStorage.getItem("adminToken"),u=await fetch(`https://pluginsight.vercel.app/api/analytics/download-data/${a.slug}?days=15`,{headers:{Authorization:`Bearer ${t}`}});if(!u.ok)throw new Error("Failed to fetch download data");const m=await u.json();if(m.success&&m.downloadData){const I=m.downloadData.map(M=>({date:new Date(M.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:M.downloads,fullDate:M.date}));c(I)}else c([])}catch(t){console.error("Error fetching download data:",t),c([])}finally{E(!1)}},ue=async()=>{if(a)try{B(!0);const t=localStorage.getItem("adminToken"),u=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-info/${a.slug}`,{headers:{Authorization:`Bearer ${t}`}});if(!u.ok)throw new Error("Failed to fetch plugin information");const m=await u.json();m.success&&m.ratings?($(m.ratings),y(m.totalRatings),S(m.averageRating||0)):($([]),y(0),S(0))}catch(t){console.error("Error fetching ratings data:",t),$([]),y(0),S(0)}finally{B(!1)}},te=async()=>{if(a)try{Q(!0);const t=localStorage.getItem("adminToken"),u=await fetch(`https://pluginsight.vercel.app/api/analytics/rank-history/${a.slug}?days=15`,{headers:{Authorization:`Bearer ${t}`}});if(!u.ok)throw new Error("Failed to fetch rank history");const m=await u.json();if(m.success&&m.rankHistory){const I=m.rankHistory.map(M=>({date:M.date,rank:M.rank,fetchedAt:M.fetchedAt}));f(I)}else f([])}catch(t){console.error("Error fetching rank history:",t),f([])}finally{Q(!1)}},ce=async()=>{if(a)try{X(!0);const t=localStorage.getItem("adminToken"),u=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-versions/${a.slug}`,{headers:{Authorization:`Bearer ${t}`}});if(!u.ok)throw new Error("Failed to fetch plugin versions");const m=await u.json();m.success?(j(m.versions||{}),P(m.currentVersion),T(m.oldVersions||{})):(j({}),P(null),T({}))}catch(t){console.error("Error fetching versions data:",t),j({}),P(null),T({})}finally{X(!1)}};r.useEffect(()=>{s&&a&&(be(),ue(),te(),ce())},[s,a]),r.useEffect(()=>{const t=h=>{o.current&&!o.current.contains(h.target)&&(C(!1),p(""))};if(k)return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[k]);const ge=t=>{var h;return t?((h=t.split(/[-–:]|&#8211;/)[0])==null?void 0:h.trim())||t:""},oe=()=>!F||Object.keys(F).length===0?[]:Object.keys(F).filter(t=>t.toLowerCase()==="trunk"||t===R?!1:g.trim()?t.toLowerCase().includes(g.toLowerCase()):!0).sort((t,h)=>{const u=M=>M.split(".").map(W=>parseInt(W)||0),m=u(t),I=u(h);for(let M=0;M<Math.max(m.length,I.length);M++){const W=(I[M]||0)-(m[M]||0);if(W!==0)return W}return 0}),pe=t=>{A(t),C(!1),p("")},x=()=>{C(!k),k||p("")},_=["#10B981","#3B82F6","#F59E0B","#EF4444","#8B5CF6"],G=t=>{const h=Math.PI/180,{cx:u,cy:m,midAngle:I,innerRadius:M,outerRadius:W,startAngle:Ne,endAngle:ve,fill:Z,payload:H,percent:Je,value:Ye}=t,Ue=Math.sin(-h*I),de=Math.cos(-h*I),Fe=u+(W+10)*de,Ge=m+(W+10)*Ue,Be=u+(W+30)*de,Ie=m+(W+30)*Ue,ke=Be+(de>=0?1:-1)*22,je=Ie,n=de>=0?"start":"end";return e.jsxs("g",{children:[e.jsxs("text",{x:u,y:m,dy:8,textAnchor:"middle",fill:Z,children:[H.stars,"★"]}),e.jsx(rs,{cx:u,cy:m,innerRadius:M,outerRadius:W,startAngle:Ne,endAngle:ve,fill:Z}),e.jsx(rs,{cx:u,cy:m,startAngle:Ne,endAngle:ve,innerRadius:W+6,outerRadius:W+10,fill:Z}),e.jsx("path",{d:`M${Fe},${Ge}L${Be},${Ie}L${ke},${je}`,stroke:Z,fill:"none"}),e.jsx("circle",{cx:ke,cy:je,r:2,fill:Z,stroke:"none"}),e.jsx("text",{x:ke+(de>=0?1:-1)*12,y:je,textAnchor:n,fill:"#333",children:`${Ye} ratings`}),e.jsx("text",{x:ke+(de>=0?1:-1)*12,y:je,dy:18,textAnchor:n,fill:"#999",children:`(${(Je*100).toFixed(1)}%)`})]})};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(Bs,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[ge((a==null?void 0:a.displayName)||(a==null?void 0:a.name))," ","Analytics"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Download trends and rating analysis"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>b(`/plugin-details/${a==null?void 0:a.slug}`),className:"flex items-center space-x-2 px-3 py-2 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded-lg transition-colors",title:"View Plugin Details",children:[e.jsx(ts,{className:"h-4 w-4"}),e.jsx("span",{children:"Plugin Details"})]}),e.jsx("button",{onClick:l,className:"text-gray-400 hover:text-gray-600 transition-colors p-2",children:e.jsx(qe,{className:"h-6 w-6"})})]})]}),e.jsxs("div",{className:"flex-1 p-6 space-y-4 overflow-y-auto",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(we,{className:"h-5 w-5 mr-2 text-blue-600"}),"Download Trends (Last 15 Days)"]}),L?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):d.length>0?e.jsx(Pe,{width:"100%",height:300,children:e.jsxs(xs,{data:d,children:[e.jsx(Te,{strokeDasharray:"3 3"}),e.jsx(Ee,{dataKey:"date"}),e.jsx(Le,{}),e.jsx(Ae,{formatter:t=>[t.toLocaleString(),"Downloads"],labelFormatter:t=>`Date: ${t}`}),e.jsx(hs,{dataKey:"downloads",fill:"#3B82F6",children:e.jsx(_e,{dataKey:"downloads",position:"top",fontSize:10,formatter:t=>t.toLocaleString()})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No download data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(we,{className:"h-5 w-5 mr-2 text-purple-600"}),"15-Day Rank Change"]}),K?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"})}):v.length>0?e.jsx(Pe,{width:"100%",height:300,children:e.jsxs(es,{data:v,children:[e.jsx(Te,{strokeDasharray:"3 3"}),e.jsx(Ee,{dataKey:"date"}),e.jsx(Le,{domain:["dataMin - 10","dataMax + 10"],reversed:!0,tickFormatter:t=>`#${t}`}),e.jsx(Ae,{formatter:t=>[`#${t}`,"Rank"],labelFormatter:t=>`Date: ${t}`}),e.jsx(ss,{type:"monotone",dataKey:"rank",stroke:v.length>1&&v[v.length-1].rank<v[0].rank?"#10B981":"#EF4444",strokeWidth:2,dot:{fill:v.length>1&&v[v.length-1].rank<v[0].rank?"#10B981":"#EF4444",strokeWidth:2,r:4},activeDot:{r:6,strokeWidth:2},children:e.jsx(_e,{dataKey:"rank",position:"top",formatter:t=>`#${t}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rank history data available"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-gray-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(ze,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Rating Distribution"]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-lg font-bold text-gray-900 flex items-center",children:[U?(U/20).toFixed(1):"N/A"," ⭐"]})})]}),O?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"})}):w.length>0?e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 flex-1",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Pe,{width:200,height:200,children:e.jsxs(Gs,{children:[e.jsx(Qs,{activeIndex:re,activeShape:G,data:[...w].sort((t,h)=>h.stars-t.stars),cx:"50%",cy:"50%",innerRadius:40,outerRadius:60,fill:"#8884d8",dataKey:"value",onMouseEnter:ne,children:w.map((t,h)=>e.jsx(Xs,{fill:_[h%_.length]},`cell-${h}`))}),e.jsx(Ae,{formatter:(t,h,u)=>[`${t} ratings`,`${u.payload.stars} Star${u.payload.stars!==1?"s":""}`]})]})})})}),e.jsxs("div",{className:"bg-white rounded-lg p-4 space-y-2 flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Breakdown"}),e.jsx("div",{className:"space-y-2",children:[...w].sort((t,h)=>h.stars-t.stars).map((t,h)=>{const u=w.length>0?t.value/w.reduce((m,I)=>m+I.value,0)*100:0;return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex items-center space-x-1 w-12",children:[e.jsx("span",{className:"text-xs font-medium text-gray-700",children:t.stars}),e.jsx(ze,{className:"h-3 w-3 text-yellow-400 fill-current"})]}),e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-500",style:{width:`${u}%`,backgroundColor:_[h%_.length]}})}),e.jsx("div",{className:"text-xs font-medium text-gray-900 w-8 text-right",children:t.value})]},t.stars)})}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-2",children:[e.jsx("div",{className:"text-xs font-bold text-gray-500",children:"Total"}),e.jsx("div",{className:"text-xs font-medium text-gray-900",children:N})]})]})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rating data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(ts,{className:"h-5 w-5 mr-2 text-blue-600"}),"Plugin Downloads"]}),Y?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):Object.keys(i).length>0?e.jsxs("div",{className:"space-y-4",children:[R&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(as,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"font-semibold text-gray-900",children:["Version ",R]}),e.jsx("span",{className:"bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full",children:"Current"})]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Latest stable release"})]})]}),e.jsx("a",{href:i[R],target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center justify-center transition-colors",title:"Download Current Version",children:e.jsx(me,{className:"h-4 w-4"})})]})}),Object.keys(F).length>0&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(as,{className:"h-5 w-5 text-gray-600"})}),e.jsx("span",{className:"font-semibold text-gray-900",children:"Previous Versions"}),e.jsx("span",{className:"bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full",children:"Archive"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",ref:o,children:[e.jsxs("button",{type:"button",onClick:x,className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left flex items-center justify-between",children:[e.jsx("span",{className:D?"text-gray-900":"text-gray-500",children:D||"Select a version"}),k?e.jsx(Is,{className:"h-4 w-4 text-gray-400"}):e.jsx(Ms,{className:"h-4 w-4 text-gray-400"})]}),k&&e.jsxs("div",{className:"absolute z-10 w-full bottom-full mb-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:[e.jsx("div",{className:"p-2 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(fe,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search versions...",value:g,onChange:t=>p(t.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent",autoFocus:!0})]})}),e.jsx("div",{className:"max-h-48 overflow-y-auto",children:oe().length>0?oe().map(t=>e.jsx("button",{type:"button",onClick:()=>pe(t),className:"w-full px-3 py-2 text-sm text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors",children:t},t)):e.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:g.trim()?"No versions found":"No versions available"})})]})]}),e.jsx("div",{children:e.jsx("a",{href:D?F[D]:"#",target:D?"_blank":"_self",rel:"noopener noreferrer",className:`w-10 h-10 rounded-lg flex items-center justify-center transition-colors ml-3 ${D?"bg-gray-600 hover:bg-gray-700 text-white cursor-pointer":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,title:D?"Download Selected Version":"Select a version first",onClick:D?void 0:t=>t.preventDefault(),children:e.jsx(me,{className:"h-4 w-4"})})})]})]})})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No version data available"})]})]})]})]})}):null},pt=s=>{if(!s||typeof s!="string")return!1;const l=s.split(".");if(l.length!==3)return!1;try{return l.forEach(a=>{if(a.length===0)throw new Error("Empty JWT part");atob(a.replace(/-/g,"+").replace(/_/g,"/"))}),!0}catch{return!1}},Ce=()=>{const s=localStorage.getItem("adminToken");return s?pt(s)?s:(console.warn("Invalid JWT token found, clearing localStorage"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),null):null},ls=(s,l)=>{var a;((l==null?void 0:l.status)===401||(a=s==null?void 0:s.message)!=null&&a.includes("token"))&&(console.warn("Authentication error detected, clearing tokens"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.pathname!=="/login"&&(window.location.href="/login"))},ft=({plugin:s,onRemove:l,onRefresh:a,canAddPlugins:b})=>{var y,U;const[o,d]=r.useState(!1),[c,w]=r.useState(!1),$=S=>{if(!S)return{formatted:"N/A",daysDiff:"N/A"};try{const v=S.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!v)return{formatted:"N/A",daysDiff:"N/A"};const[,f,i,j]=v,R=`${j}-${i}-${f}`,P=new Date(`${f}-${i}-${j}`),F=new Date;if(isNaN(P.getTime()))return{formatted:"N/A",daysDiff:"N/A"};const T=F-P,D=Math.floor(T/(1e3*60*60*24));return{formatted:R,daysDiff:D}}catch{return{formatted:"N/A",daysDiff:"N/A"}}},N=async()=>{if(a){w(!0);try{await a(s.slug)}finally{w(!1)}}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2 flex-1 overflow-hidden",children:[e.jsxs("div",{className:"w-12 h-12 border rounded-lg flex items-center justify-center overflow-hidden",children:[s.icons&&(s.icons["2x"]||s.icons["1x"])?e.jsx("img",{src:s.icons["2x"]||s.icons["1x"],alt:`${s.displayName} icon`,className:"w-full h-full object-cover rounded-lg",onError:S=>{S.target.style.display="none",S.target.nextSibling.style.display="flex"}}):null,e.jsx(ds,{className:`h-6 w-6 text-black ${s.icons&&(s.icons["2x"]||s.icons["1x"])?"hidden":""}`})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-semibold text-gray-900 truncate text-lg",children:s.displayName}),e.jsx("p",{className:"text-sm text-gray-500 font-mono whitespace-nowrap",children:s.slug})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("button",{onClick:N,disabled:c,className:"text-gray-400 hover:text-green-500 transition-colors p-1 disabled:opacity-50",title:"Refresh plugin data",children:e.jsx(se,{className:`h-4 w-4 ${c?"animate-spin":""}`})}),b&&e.jsx("button",{onClick:()=>l(s.slug),className:"text-gray-400 hover:text-red-500 transition-colors p-1",title:"Remove plugin",children:e.jsx($e,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between space-x-4",children:[e.jsxs("span",{className:"text-sm font-medium px-2 py-1 rounded-full bg-green-100 text-green-800",children:["v",s.version||"N/A"]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",s.currentRank||"N/A"]}),((y=s.rankHistory)==null?void 0:y.rankChange)!==null&&((U=s.rankHistory)==null?void 0:U.rankChange)!==void 0&&e.jsxs("span",{className:`text-xs ${s.rankHistory.rankChange>0?"text-green-600":s.rankHistory.rankChange<0?"text-red-600":"text-gray-600"}`,children:[s.rankHistory.rankChange>0?"↑":s.rankHistory.rankChange<0?"↓":"→",Math.abs(s.rankHistory.rankChange)]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Released"}),e.jsx("div",{className:`text-sm font-medium px-2 py-1 rounded ${(()=>{const S=s.lastReleaseDate||s.lastFetched,v=$(S);return v.daysDiff==="N/A"||v.daysDiff<=20?"bg-gray-100 text-gray-700":"bg-yellow-50 text-yellow-700"})()}`,children:(()=>{const S=s.lastReleaseDate||s.lastFetched,v=$(S);return e.jsxs(e.Fragment,{children:[v.formatted,v.daysDiff!=="N/A"&&e.jsxs("span",{className:"text-xs ml-1",children:["(",v.daysDiff," days)"]})]})})()})]})]}),e.jsxs("div",{className:"mt-4 border border-gray-200 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 px-3 py-2 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:"Download Trends"}),s.downloadTrend&&e.jsxs("span",{className:`text-xs px-2 py-1 rounded-full ${s.downloadTrend.isPositive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[s.downloadTrend.isPositive?"↑":"↓"," ",s.downloadTrend.changePercent,"%"]})]})}),s.downloadTrend?e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxs("tbody",{className:"bg-white divide-y divide-gray-200",children:[e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Yesterday"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-700 font-bold text-right",children:s.downloadTrend.yesterdayDownloads.toLocaleString()})]}),e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Day Before"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-900 font-bold text-right",children:s.downloadTrend.dayBeforeDownloads.toLocaleString()})]})]}),e.jsx("tfoot",{className:"bg-gray-100 border-t border-gray-200",children:e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-2 text-sm font-semibold text-gray-900",children:"Changes"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-right",children:e.jsxs("span",{className:`text-sm font-bold ${s.downloadTrend.change>=0?"text-green-600":"text-red-600"}`,children:[s.downloadTrend.change>=0?"+":"",s.downloadTrend.change.toLocaleString()]})})]})})]}):e.jsx("div",{className:"text-center py-4",children:e.jsx("span",{className:"text-xs text-gray-500",children:"No download trend data available"})})]}),e.jsx("div",{className:"pt-4 border-t border-gray-100",children:e.jsxs("button",{onClick:()=>d(!0),className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-2",children:[e.jsx(Os,{className:"h-4 w-4"}),e.jsx("span",{children:"View Chart & Analytics"})]})})]}),e.jsx(gt,{isOpen:o,onClose:()=>d(!1),plugin:s})]})},yt=()=>{var pe,x,_,G;const{user:s,autoLogout:l}=ye(),[a,b]=r.useState(!1),[o,d]=r.useState(""),[c,w]=r.useState(!1),[$,N]=r.useState(!1),[y,U]=r.useState(null),[S,v]=r.useState([]),[f,i]=r.useState(null),[j,R]=r.useState(!1),[P,F]=r.useState(!1),[T,D]=r.useState(null),[A,g]=r.useState(!1),[p,k]=r.useState(!1),[C,L]=r.useState(null),[E,O]=r.useState(""),B=s&&["admin","superadmin"].includes(s.role),K=async()=>{try{const t=Ce();if(!t){console.warn("No valid authentication token found for database check"),l("No valid authentication token found. Please login again.");return}const u=await fetch("https://pluginsight.vercel.app/api/plugins/check-database",{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(u.ok){const m=await u.json();m.success&&N(m.hasPlugins)}else if(u.status===401){console.warn("Authentication failed during database check");let m="Your session has expired. Please login again.";try{const I=await u.json();I.message&&(m=I.message)}catch{}l(m)}else ls(null,u)}catch(t){if(console.error("Error checking database status:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during database check:",t.message),l(t.message);return}ls(t)}},Q=async()=>{try{w(!0),U({current:0,total:0,page:0,totalPages:0,successCount:0,errorCount:0,percentComplete:0,estimatedTimeRemaining:null,averageTimePerPage:null,pluginsPerSecond:0,message:"Starting full plugin fetch (all 55,540+ plugins)..."});const t=Ce();if(!t){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const u=await fetch("https://pluginsight.vercel.app/api/plugins/fetch-all",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!u.ok){if(u.status===401){console.warn("Authentication failed during plugin fetch");let M="Your session has expired. Please login again.";try{const W=await u.json();W.message&&(M=W.message)}catch{}l(M);return}throw new Error(`HTTP error! status: ${u.status}`)}const m=u.body.getReader(),I=new TextDecoder;for(;;){const{done:M,value:W}=await m.read();if(M)break;const ve=I.decode(W).split(`
`).filter(Z=>Z.trim());for(const Z of ve)try{const H=JSON.parse(Z);if(H.type==="progress")U({current:H.current||0,total:H.total||0,page:H.page||0,totalPages:H.totalPages||0,successCount:H.successCount||0,errorCount:H.errorCount||0,percentComplete:H.percentComplete||0,estimatedTimeRemaining:H.estimatedTimeRemaining,averageTimePerPage:H.averageTimePerPage,pluginsPerSecond:H.pluginsPerSecond||0,message:H.message||"Processing..."});else if(H.type==="complete")U({current:H.summary.totalProcessedPlugins,total:H.summary.totalPlugins,page:H.summary.totalPages,totalPages:H.summary.totalPages,successCount:H.summary.successfulPages,errorCount:H.summary.failedPages,percentComplete:100,averageTimePerPage:H.summary.averageTimePerPage,pluginsPerSecond:H.summary.averagePluginsPerSecond,successRate:H.summary.successRate,totalDuration:H.summary.totalDuration,message:`✅ Fetch completed! ${H.summary.totalProcessedPlugins.toLocaleString()} plugins processed in ${Math.round(H.summary.totalDuration/1e3/60)} minutes`}),H.errors&&H.errors.length>0&&console.warn("Some errors occurred during fetch:",H.errors),window.toast(`Successfully fetched ${H.summary.totalProcessedPlugins.toLocaleString()} plugins!`,"success");else if(H.type==="error")throw new Error(H.message||"Fetch failed")}catch(H){console.warn("Failed to parse streaming data:",H)}}}catch(t){if(console.error("Fetch all plugins error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin fetch:",t.message),l(t.message);return}U({current:0,total:0,message:`❌ Full fetch failed: ${t.message}`,error:!0}),window.toast(`Fetch failed: ${t.message}`,"error")}finally{w(!1),setTimeout(()=>U(null),1e4),K()}},Y=async()=>{try{const t=await He("/api/plugins/added");if(!t.ok){let u=`HTTP error! status: ${t.status}`;try{const m=await t.json();m.message&&(u=m.message)}catch{}throw new Error(u)}const h=await t.json();if(h.success){const u=h.addedPlugins.map(m=>{var I;return{slug:m.pluginSlug,name:m.pluginName,displayName:m.displayName,currentRank:m.currentRank,rankGrowth:((I=m.rankHistory)==null?void 0:I.rankChange)||0,lastFetched:m.lastUpdated,short_description:m.short_description,version:m.version,lastReleaseDate:m.lastReleaseDate,icons:m.icons||{},rating:m.rating,numRatings:m.numRatings||0,currentVersion:m.currentVersion,previousVersions:m.previousVersions||[],rankHistory:m.rankHistory,downloadTrend:m.downloadTrend,downloadDataHistory:m.downloadDataHistory||[],reviewStats:m.reviewStats,versionInfo:m.versionInfo,pluginInformation:m.pluginInformation}});v(u)}else console.warn("Failed to load added plugins:",h.message)}catch(t){console.error("Error loading added plugins:",t),t.name==="TypeError"&&t.message.includes("Failed to fetch")?(console.warn("Unable to connect to server - backend may be down"),v([])):(console.warn("Failed to load added plugins:",t.message),v([]))}},X=t=>{try{localStorage.setItem("pluginData",JSON.stringify(t))}catch(h){console.error("Error storing plugin data in localStorage:",h)}},re=()=>{try{const t=localStorage.getItem("pluginData");return t?JSON.parse(t):null}catch(t){return console.error("Error retrieving plugin data from localStorage:",t),null}},le=()=>{localStorage.removeItem("pluginData")},ne=async()=>{if(!o.trim()){window.toast("Please enter a plugin slug","warning");return}try{R(!0);const t=`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${o.trim()}&request[fields][icons]=true`,h=await fetch(t);if(!h.ok)throw new Error(`WordPress API error: ${h.status}`);const u=await h.json();if(u.error){window.toast(`Plugin not found: ${u.error}`,"error"),i(null);return}const m={slug:u.slug,name:u.name,version:u.version,author:u.author,rating:u.rating,active_installs:u.active_installs,num_ratings:u.num_ratings,downloaded:u.downloaded,last_updated:u.last_updated,short_description:u.short_description,homepage:u.homepage,requires:u.requires,tested:u.tested,requires_php:u.requires_php,icons:u.icons?{"1x":u.icons["1x"],"2x":u.icons["2x"]}:{},tags:u.tags?Object.keys(u.tags).slice(0,10):[]};X(m),i({slug:u.slug,name:u.name,version:u.version,author:u.author,rating:u.rating,active_installs:u.active_installs,num_ratings:u.num_ratings,downloaded:u.downloaded,last_updated:u.last_updated,homepage:u.homepage,requires:u.requires,tested:u.tested,requires_php:u.requires_php}),window.toast("Plugin data fetched successfully from WordPress API","success")}catch(t){console.error("Error fetching plugin data:",t),window.toast("Failed to fetch plugin data from WordPress API","error"),i(null)}finally{R(!1)}};r.useEffect(()=>{Y(),K()},[]);const be=async()=>{const t=re();if(!t){window.toast("Please fetch plugin data first by clicking the Fetch button","warning");return}if(!o.trim()){window.toast("Please enter a plugin slug","warning");return}g(!0);try{const h=Ce();if(!h){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const m=await fetch("https://pluginsight.vercel.app/api/plugins/added-with-data",{method:"POST",headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"},body:JSON.stringify({slug:o.trim(),pluginData:t})});if(!m.ok){if(m.status===401){console.warn("Authentication failed during plugin addition");let M="Your session has expired. Please login again.";try{const W=await m.json();W.message&&(M=W.message)}catch{}l(M);return}else if(m.status===413){console.warn("Payload too large error during plugin addition");try{const M=await m.json();window.toast(M.message||"Plugin data is too large. Please try again or contact support.","error")}catch{window.toast("Plugin data is too large. Please try again or contact support.","error")}return}}const I=await m.json();I.success?(window.toast(I.message,"success"),le(),d(""),b(!1),i(null),await Y()):window.toast(I.message||"Failed to add plugin","error")}catch(h){if(console.error("Add plugin error:",h),h.message&&(h.message.includes("expired")||h.message.includes("token")||h.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin addition:",h.message),l(h.message);return}window.toast("Failed to add plugin. Please try again.","error")}finally{g(!1)}},ue=t=>{const h=S.find(u=>u.slug===t)||addedPluginsListData.find(u=>u.slug===t);D(h),F(!0)},te=async()=>{if(T)try{const t=Ce();if(!t){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const u=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${T.slug}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!u.ok&&u.status===401){console.warn("Authentication failed during plugin removal");let I="Your session has expired. Please login again.";try{const M=await u.json();M.message&&(I=M.message)}catch{}l(I);return}const m=await u.json();m.success?(window.toast("Plugin removed successfully","success"),await Y()):window.toast(m.message||"Failed to remove plugin","error")}catch(t){if(console.error("Remove plugin error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin removal:",t.message),l(t.message);return}window.toast("Failed to remove plugin","error")}finally{F(!1),D(null)}},ce=async t=>{try{const h=Ce();if(!h){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const m=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${t}/refresh`,{method:"POST",headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}});if(!m.ok&&m.status===401){console.warn("Authentication failed during plugin refresh");let M="Your session has expired. Please login again.";try{const W=await m.json();W.message&&(M=W.message)}catch{}l(M);return}const I=await m.json();I.success?(window.toast(I.message,"success"),await Y()):window.toast(I.message||"Failed to refresh plugin","error")}catch(h){if(console.error("Refresh plugin error:",h),h.message&&(h.message.includes("expired")||h.message.includes("token")||h.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin refresh:",h.message),l(h.message);return}window.toast("Failed to refresh plugin","error")}},ge=async()=>{try{k(!0),L({current:0,total:0,currentPlugin:null});const t=await He("/api/plugins/added");if(!t.ok)throw new Error(`Failed to fetch plugins: ${t.status}`);const h=await t.json();if(!h.success||!h.addedPlugins||h.addedPlugins.length===0){window.toast("No plugins found to refresh","warning");return}const u=h.addedPlugins.map(m=>m.slug);L({current:0,total:u.length,currentPlugin:null}),window.toast(`Starting refresh of ${u.length} plugins...`,"info");for(const m of u){const I=u.indexOf(m)+1;L({current:I,total:u.length,currentPlugin:m});try{const W=await(await He(`/api/plugins/added/${m}/refresh`,{method:"POST"})).json();W.success?console.log(`Successfully refreshed plugin: ${m}`):console.warn(`Failed to refresh plugin ${m}: ${W.message}`)}catch(M){console.error(`Error refreshing plugin ${m}:`,M)}}window.toast("All plugins refreshed successfully!","success"),await Y()}catch(t){if(console.error("Refresh all plugins error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during refresh all:",t.message),l(t.message);return}window.toast("Failed to refresh all plugins","error")}finally{k(!1),L(null)}},oe=S.filter(t=>{var u,m;if(!E.trim())return!0;const h=E.toLowerCase();return((u=t.name)==null?void 0:u.toLowerCase().includes(h))||((m=t.slug)==null?void 0:m.toLowerCase().includes(h))}).sort((t,h)=>{const u=t.name||t.slug||"",m=h.name||h.slug||"";return u.toLowerCase().localeCompare(m.toLowerCase())});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold text-gray-900",children:"Welcome to Admin Dashboard"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your plugins, analyze keywords, and track performance all in one place."})]}),e.jsxs("div",{className:"flex gap-4",children:[B&&e.jsx("button",{onClick:Q,disabled:c,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${c?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white`,title:"Fetch all 55,540+ plugins from WordPress repository",children:c?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Fetching All..."]}):e.jsxs(e.Fragment,{children:[e.jsx(me,{className:"h-4 w-4 mr-2"}),$?"Refetch":"Fetch"]})}),B&&e.jsxs("button",{onClick:()=>b(!0),disabled:c,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${c?"bg-gray-400 cursor-not-allowed":"bg-green-600 hover:bg-green-700"} text-white`,children:[e.jsx(Se,{className:"h-4 w-4 mr-2"}),"Add Plugin"]})]})]}),y&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg border ${y.error?"bg-red-50 border-red-200":"bg-blue-50 border-blue-200"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:`text-sm font-medium ${y.error?"text-red-900":"text-blue-900"}`,children:y.message}),y.total>0&&e.jsxs("span",{className:`text-sm ${y.error?"text-red-700":"text-blue-700"}`,children:[(pe=y.current)==null?void 0:pe.toLocaleString(),"/",(x=y.total)==null?void 0:x.toLocaleString()]})]}),y.page&&y.totalPages&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-700",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["Page: ",y.page,"/",y.totalPages]}),e.jsxs("div",{children:["Progress: ",y.percentComplete||0,"%"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["✅ ",(_=y.successCount)==null?void 0:_.toLocaleString()," success"]}),y.errorCount>0&&e.jsxs("div",{className:"text-red-600",children:["❌ ",(G=y.errorCount)==null?void 0:G.toLocaleString()," errors"]})]})]}),(y.pluginsPerSecond>0||y.estimatedTimeRemaining)&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-600",children:[y.pluginsPerSecond>0&&e.jsxs("div",{children:["Speed: ",y.pluginsPerSecond," plugins/sec"]}),y.estimatedTimeRemaining&&e.jsxs("div",{children:["ETA:"," ",Math.round(y.estimatedTimeRemaining/1e3/60)," ","min"]}),y.averageTimePerPage&&e.jsxs("div",{children:["Avg: ",Math.round(y.averageTimePerPage/1e3),"s/page"]}),y.successRate&&e.jsxs("div",{children:["Success Rate: ",y.successRate,"%"]})]}),y.totalDuration&&e.jsx("div",{className:"mb-3 text-xs text-green-700 bg-green-50 p-2 rounded",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:["Duration:"," ",Math.round(y.totalDuration/1e3/60)," ","minutes"]}),e.jsxs("div",{children:["Avg Speed: ",y.pluginsPerSecond," plugins/sec"]}),e.jsxs("div",{children:["Success Rate: ",y.successRate,"%"]}),e.jsxs("div",{children:["Pages: ",y.successCount,"/",y.totalPages]})]})}),y.total>0&&!y.error&&e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-3",children:e.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-300 flex items-center justify-center",style:{width:`${Math.max(2,y.percentComplete||y.current/y.total*100)}%`},children:e.jsxs("span",{className:"text-xs text-white font-medium",children:[y.percentComplete||Math.round(y.current/y.total*100),"%"]})})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"Added Plugins"}),e.jsx("div",{className:"text-sm text-gray-500",children:S.length>0?`Showing ${oe.length} of ${S.length} plugins`:"No plugins added yet"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[S.length>0&&e.jsxs("div",{className:"relative",children:[e.jsx(fe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search plugins...",value:E,onChange:t=>O(t.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"})]}),B&&S.length>0&&e.jsx("button",{onClick:ge,disabled:p,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${p?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white text-sm`,title:"Refresh all plugins sequentially",children:p?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Refreshing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(se,{className:"h-4 w-4 mr-2"}),"Refresh All"]})})]})]}),C&&e.jsxs("div",{className:"mb-6 p-4 rounded-lg border bg-blue-50 border-blue-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Refreshing plugins..."}),e.jsxs("span",{className:"text-sm text-blue-700",children:[C.current,"/",C.total]})]}),C.currentPlugin&&e.jsxs("div",{className:"text-sm text-blue-700 mb-2",children:["Currently refreshing:"," ",e.jsx("span",{className:"font-mono",children:C.currentPlugin})]}),e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.max(2,C.current/C.total*100)}%`}})}),e.jsx("div",{className:"text-xs text-blue-600 mt-2",children:"Note: Each plugin refresh has a 60-second delay to avoid overwhelming the backend."})]}),S.length>0?oe.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:oe.map(t=>e.jsx(ft,{plugin:t,onRemove:ue,onRefresh:ce,canAddPlugins:B},t.slug))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(fe,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins found"}),e.jsxs("p",{className:"text-gray-600",children:['No plugins match your search query "',E,'". Try adjusting your search terms.']})]}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(ds,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins added yet"}),e.jsx("p",{className:"text-gray-600",children:'Start tracking your WordPress plugins by adding them to your dashboard using the "Add Plugin" button above.'})]})]}),e.jsx(he,{isOpen:a,onClose:()=>{A||(b(!1),i(null),d(""),le())},title:"Add New Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pluginSlug",className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{id:"pluginSlug",type:"text",value:o,onChange:t=>d(t.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., my-awesome-plugin"}),e.jsx("button",{onClick:ne,disabled:j||!o.trim(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:j?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Fetching..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(me,{className:"h-4 w-4"}),e.jsx("span",{children:"Fetch"})]})})]})]}),f&&e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 space-y-3",children:[e.jsx("h4",{className:"font-semibold text-green-900",children:"Plugin Information (Fetched from WordPress API)"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Name:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Version:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.version||"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Author:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:(()=>{const t=f.author;if(!t)return"N/A";const h=t.match(/<a[^>]*>(.*?)<\/a>/);return h?h[1]:t})()})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Rating:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.rating?`${f.rating}/100`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Active Installs:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.active_installs?f.active_installs.toLocaleString():"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Last Updated:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.last_updated||"N/A"})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("span",{className:"text-green-700",children:"WordPress Requirements:"}),e.jsxs("span",{className:"ml-2 font-medium text-green-900",children:["WP ",f.requires||"N/A"," | Tested up to"," ",f.tested||"N/A"," | PHP"," ",f.requires_php||"N/A"]})]})]})]}),!f&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("p",{className:"text-blue-800 text-sm",children:[e.jsx("strong",{children:"Step 1:"}),' Enter a plugin slug and click "Fetch" to retrieve plugin information from WordPress API.',e.jsx("br",{}),e.jsx("strong",{children:"Step 2:"}),' Once plugin data is displayed, click "Add Plugin" to add it to your dashboard.']})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{b(!1),i(null),d(""),le()},disabled:A,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),e.jsx("button",{onClick:be,disabled:A||!f,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:A?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Adding..."})]}):e.jsx("span",{children:"Add Plugin"})})]})]})}),e.jsx(he,{isOpen:P,onClose:()=>{F(!1),D(null)},title:"Confirm Delete",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx($e,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Delete Plugin"}),e.jsxs("p",{className:"text-gray-600",children:['Are you sure you want to remove "',(T==null?void 0:T.displayName)||(T==null?void 0:T.name),'" from your added plugins?']})]})]}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm text-yellow-800",children:[e.jsx("strong",{children:"Warning:"})," This action cannot be undone. The plugin will be removed from your dashboard and you'll need to add it again if you want to track it."]})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{F(!1),D(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsxs("button",{onClick:te,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2",children:[e.jsx($e,{className:"h-4 w-4"}),e.jsx("span",{children:"Delete Plugin"})]})]})]})})]})},bt=()=>{var T,D,A;const[s,l]=r.useState([]),[a,b]=r.useState(""),[o,d]=r.useState("7"),[c,w]=r.useState({start:"",end:""}),[$,N]=r.useState([]),[y,U]=r.useState(!1),[S,v]=r.useState(!1);console.log("Chart data: ",$);const f=g=>{var K;const{cx:p,cy:k,payload:C}=g;if(!a||!C)return null;const L=((K=s.find(Q=>Q.pluginSlug===a))==null?void 0:K.displayName)||a,E=C[`${L}_trend`],O=C[L];if(O==null)return null;let B="#3B82F6";return E==="improvement"?B="#10B981":E==="decline"&&(B="#EF4444"),e.jsx("circle",{cx:p,cy:k,r:5,fill:B,stroke:B,strokeWidth:2})},i=async()=>{var g;try{const p=localStorage.getItem("adminToken"),C=await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${p}`,"Content-Type":"application/json"}});if(!C.ok)throw new Error(`HTTP error! status: ${C.status}`);const L=await C.json();L.success?(l(L.plugins),console.log(`✅ Loaded ${((g=L.plugins)==null?void 0:g.length)||0} plugins from plugininformations collection (${L.pluginsWithRankHistory||0} with rank history)`)):console.warn("Failed to load plugins:",L.message)}catch(p){console.error("Error loading plugins from plugininformations collection:",p),p.name==="TypeError"&&p.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins from database","error")}},j=async()=>{if(!a){N([]);return}try{U(!0);const g=s.find(k=>k.pluginSlug===a);if(!g){console.error("Selected plugin not found in loaded plugins"),window.toast("Selected plugin not found","error"),N([]);return}if(!g.rankHistory||!Array.isArray(g.rankHistory)){console.log(`Plugin ${a} has no rank history data`),N([]);return}console.log(`📊 Processing rank history for ${a}: ${g.rankHistory.length} entries`);const p=R(g);N(p)}catch(g){console.error("Error loading chart data:",g),window.toast("Failed to load chart data","error")}finally{U(!1)}},R=g=>{if(!g.rankHistory||!Array.isArray(g.rankHistory))return[];const p=g.displayName||g.pluginName||g.pluginSlug;let k=g.rankHistory;if(o!=="custom"){const E=parseInt(o),O=new Date;O.setDate(O.getDate()-E),k=g.rankHistory.filter(B=>{const[K,Q,Y]=B.date.split("-");return new Date(Y,Q-1,K)>=O})}else if(c.start&&c.end){const E=new Date(c.start),O=new Date(c.end);k=g.rankHistory.filter(B=>{const[K,Q,Y]=B.date.split("-"),X=new Date(Y,Q-1,K);return X>=E&&X<=O})}return k.sort((E,O)=>{const[B,K,Q]=E.date.split("-"),[Y,X,re]=O.date.split("-"),le=new Date(Q,K-1,B),ne=new Date(re,X-1,Y);return le-ne}).map((E,O,B)=>{const K=B[O-1];let Q="stable";return K&&(E.previousRank<K.previousRank?Q="improvement":E.previousRank>K.previousRank&&(Q="decline")),{date:E.date,[p]:E.previousRank,[`${p}_trend`]:Q}})},P=async()=>{if(!a){window.toast("Please select a plugin first","warning");return}try{U(!0),window.toast("Refreshing chart data...","info"),await j(),window.toast("Chart data refreshed successfully","success")}catch(g){console.error("Error refreshing chart data:",g),window.toast("Failed to refresh chart data","error")}finally{U(!1)}};r.useEffect(()=>{i()},[]),r.useEffect(()=>{j()},[a,o,c]);const F=g=>{d(g),v(g==="custom")};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(we,{className:"h-8 w-8 text-blue-600 mr-3"}),"Plugin Rank Analysis"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Track ranking trends for your added plugins"})]})})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plugin"}),e.jsxs("select",{value:a,onChange:g=>b(g.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(g=>e.jsx("option",{value:g.pluginSlug,children:g.displayName},g.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:o,onChange:g=>F(g.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom range"})]})]}),S&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:c.start,onChange:g=>w(p=>({...p,start:g.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:c.end,onChange:g=>w(p=>({...p,end:g.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:P,disabled:y||!a,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Fetch latest rank from WordPress and save to database",children:[e.jsx(se,{className:`h-4 w-4 mr-2 ${y?"animate-spin":""}`}),"Refresh"]})})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:["Plugin Rank Trends",a&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["(",((T=s.find(g=>g.pluginSlug===a))==null?void 0:T.displayName)||a,")"]})]}),y?e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(se,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading chart data..."})]})}):$.length>0?e.jsx("div",{className:"h-96",children:e.jsx(Pe,{width:"100%",height:"100%",children:e.jsxs(es,{data:$,children:[e.jsx(Te,{strokeDasharray:"3 3"}),e.jsx(Ee,{dataKey:"date",tick:{fontSize:12},angle:-45,textAnchor:"end",height:60}),e.jsx(Le,{tick:{fontSize:12},domain:(()=>{var Q;if($.length===0)return[1,100];const g=((Q=s.find(Y=>Y.pluginSlug===a))==null?void 0:Q.displayName)||a,p=$.map(Y=>Y[g]).filter(Y=>Y!=null);if(p.length===0)return[1,100];const k=Math.min(...p),C=Math.max(...p),L=p[p.length-1],E=Math.max(1,L-10),O=L+10,B=Math.min(E,k-2),K=Math.max(O,C+2);return[B,K]})(),reversed:!0,label:{value:"Rank",angle:-90,position:"insideLeft"},allowDecimals:!1,type:"number"}),e.jsx(Ae,{labelFormatter:g=>`Date: ${g}`,formatter:(g,p)=>[g?`#${g}`:"No data",p]}),e.jsx(Zs,{}),a&&e.jsx(ss,{type:"monotone",dataKey:((D=s.find(g=>g.pluginSlug===a))==null?void 0:D.displayName)||a,stroke:"#3B82F6",strokeWidth:2,dot:e.jsx(f,{}),connectNulls:!1,activeDot:{r:6,stroke:"#3B82F6",strokeWidth:2},children:e.jsx(_e,{dataKey:((A=s.find(g=>g.pluginSlug===a))==null?void 0:A.displayName)||a,position:"top",fontSize:10,fill:"#374151",formatter:g=>g?`#${g}`:""})},a)]})})}):e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(we,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Data Available"}),e.jsx("p",{className:"text-gray-600 mb-4",children:a?"No rank history found for the selected plugin and date range":"Select a plugin to view its rank trends"}),s.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Add plugins from the Dashboard first to see their ranking trends."})]})})]})]})},js=s=>{const l=document.createElement("textarea");return l.innerHTML=s,l.value},jt=(s,l=40)=>{const a=js(s);if(a.length<=l)return a;const b=a.split(" ");let o=b[0];for(let d=1;d<b.length&&(o+" "+b[d]).length<=l-3;d++)o+=" "+b[d];return o+"..."},wt=()=>{var je;const[s,l]=r.useState("performance"),[a,b]=r.useState([]),[o,d]=r.useState(""),[c,w]=r.useState([]),[$,N]=r.useState(!1),[y,U]=r.useState(!1),[S,v]=r.useState(""),[f,i]=r.useState(""),[j,R]=r.useState(!1),[P,F]=r.useState(new Set),[T,D]=r.useState(!1),[A,g]=r.useState(new Set),[p,k]=r.useState(null),[C,L]=r.useState(!1),[E,O]=r.useState([]),[B,K]=r.useState(!1),[Q,Y]=r.useState(!1),[X,re]=r.useState(""),[le,ne]=r.useState({}),[be,ue]=r.useState(!1),[te,ce]=r.useState(10),[ge,oe]=r.useState(!1),[pe,x]=r.useState(!1),[_,G]=r.useState(null),[t,h]=r.useState([]),[u,m]=r.useState([]),[I,M]=r.useState(!1),[W,Ne]=r.useState(!1),ve=async()=>{var n;try{const z=localStorage.getItem("adminToken"),J=await(await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();J.success?(b(J.plugins),console.log(`✅ Loaded ${((n=J.plugins)==null?void 0:n.length)||0} plugins from plugininformations collection for keyword analysis`)):console.error("Failed to load plugins:",J.message)}catch(z){console.error("Error loading plugins from plugininformations collection:",z),window.toast("Failed to load plugins from database","error")}},Z=async()=>{try{if(N(!0),!o){w([]),ne({}),N(!1);return}const n=localStorage.getItem("adminToken"),q=`https://pluginsight.vercel.app/api/keywords?pluginSlug=${o}`,J=await(await fetch(q,{headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}})).json();if(J.success){const ae=J.keywords.map(xe=>({...xe,position:xe.latestRank,lastChecked:xe.lastChecked||xe.updatedAt}));w(ae);const ee={};ae.forEach(xe=>{ee[xe._id]=xe.occurrences||0}),ne(ee),ce(10)}else console.error("Failed to load keywords:",J.message),window.toast(J.message||"Failed to load keywords","error"),w([]),ne({})}catch(n){console.error("Error loading keywords:",n),window.toast("Failed to load keywords","error"),w([]),ne({})}finally{N(!1)}},H=async()=>{if(!f||!S.trim()){window.toast("Please select a plugin and enter a keyword","error");return}try{const n=localStorage.getItem("adminToken"),z=a.find(ae=>ae.pluginSlug===f),J=await(await fetch("https://pluginsight.vercel.app/api/keywords",{method:"POST",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:f,pluginName:(z==null?void 0:z.displayName)||f,keyword:S.trim()})})).json();J.success?(window.toast("Keyword added successfully","success"),v(""),i(""),U(!1),f===o&&Z()):window.toast(J.message||"Failed to add keyword","error")}catch(n){console.error("Error adding keyword:",n),window.toast("Failed to add keyword","error")}},Je=async()=>{try{R(!0),window.toast("Refreshing keyword ranks...","info");const n=localStorage.getItem("adminToken"),V=await(await fetch("https://pluginsight.vercel.app/api/keywords/refresh-ranks",{method:"POST",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}})).json();V.success?(window.toast(V.message,"success"),await Z()):window.toast(V.message||"Failed to refresh keyword ranks","error")}catch(n){console.error("Error refreshing keyword ranks:",n),window.toast("Failed to refresh keyword ranks","error")}finally{R(!1)}},Ye=async()=>{try{const n=localStorage.getItem("adminToken"),z="https://pluginsight.vercel.app",q=Array.from(P),J=await(await fetch(`${z}/api/keywords/bulk-delete`,{method:"DELETE",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"},body:JSON.stringify({keywordIds:q})})).json();J.success?(window.toast(`${q.length} keywords deleted successfully`,"success"),F(new Set),D(!1),Z()):window.toast(J.message||"Failed to delete keywords","error")}catch(n){console.error("Error deleting keywords:",n),window.toast("Failed to delete keywords","error")}},Ue=async()=>{if(p)try{const n=localStorage.getItem("adminToken"),V=await(await fetch(`https://pluginsight.vercel.app/api/keywords/${p}`,{method:"DELETE",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}})).json();if(V.success){window.toast("Keyword deleted successfully","success"),L(!1),k(null);const J=new Set(A);J.delete(p),g(J),Z()}else window.toast(V.message||"Failed to delete keyword","error")}catch(n){console.error("Error deleting keyword:",n),window.toast("Failed to delete keyword","error")}},de=async(n=!1)=>{try{K(!0);const z=localStorage.getItem("adminToken"),ae=await(await fetch(`https://pluginsight.vercel.app/api/competitors${n?"?autoDiscover=true":""}`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();ae.success&&(O(ae.competitors),n&&ae.competitors.length>0&&window.toast(`Discovered ${ae.competitors.length} competitor plugins`,"success"))}catch(z){console.error("Error loading competitors:",z),window.toast("Failed to load competitors","error")}finally{K(!1)}},Fe=async()=>{if(!X.trim()){window.toast("Please enter a plugin slug","error");return}try{const n=localStorage.getItem("adminToken"),V=await(await fetch("https://pluginsight.vercel.app/api/competitors",{method:"POST",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:X.trim()})})).json();V.success?(window.toast("Competitor added successfully","success"),re(""),Y(!1),de()):window.toast(V.message||"Failed to add competitor","error")}catch(n){console.error("Error adding competitor:",n),window.toast("Failed to add competitor","error")}},Ge=async n=>{G(n),ue(!0),M(!0);try{const z=localStorage.getItem("adminToken"),J=await(await fetch(`https://pluginsight.vercel.app/api/keywords/ranks/${encodeURIComponent(n.keyword)}/${n.pluginSlug}?limit=30`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();J.success?h(J.rankHistory):(window.toast("Failed to load rank history","error"),h([]))}catch(z){console.error("Error loading rank history:",z),window.toast("Failed to load rank history","error"),h([])}finally{M(!1)}},Be=async n=>{G(n),x(!0),Ne(!0);try{const q=`https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(n.keyword)}&request[per_page]=50&request[fields][active_installs]=true&request[fields][ratings]=true&request[fields][tested]=true&request[fields][last_updated]=true`,J=await(await fetch(q)).json();if(J&&J.plugins){const ae=J.plugins.filter(ee=>ee.slug!==n.pluginSlug).sort((ee,xe)=>(xe.active_installs||0)-(ee.active_installs||0)).slice(0,10).map(ee=>({pluginName:ee.name,pluginSlug:ee.slug,activeInstalls:ee.active_installs||0,rating:ee.rating||0,numRatings:ee.num_ratings||0,testedUpTo:ee.tested||"N/A",lastUpdated:ee.last_updated||"N/A",wordpressUrl:`https://wordpress.org/plugins/${ee.slug}/`}));m(ae)}else window.toast("Failed to load related plugins","error"),m([])}catch(z){console.error("Error loading related plugins:",z),window.toast("Failed to load related plugins","error"),m([])}finally{Ne(!1)}};r.useEffect(()=>{ve()},[]),r.useEffect(()=>{Z()},[o]);const Ie=()=>{te<c.length&&!ge&&(oe(!0),setTimeout(()=>{ce(n=>Math.min(n+10,c.length)),oe(!1)},500))},ke=n=>{const{scrollTop:z,scrollHeight:q,clientHeight:V}=n.target;q-z<=V+100&&Ie()};return r.useEffect(()=>{s==="competitors"&&de(!0)},[s]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6",children:e.jsx("div",{children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(fe,{className:"h-6 w-6 text-blue-600 mr-2"}),"Keyword Analysis"]})})}),e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsxs("button",{onClick:()=>l("performance"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="performance"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(we,{className:"h-4 w-4 inline mr-2"}),"Keyword Performance"]}),e.jsxs("button",{onClick:()=>l("competitors"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="competitors"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(Re,{className:"h-4 w-4 inline mr-2"}),"Competitors"]})]})})]}),s==="performance"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-stretch md:items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 whitespace-nowrap",children:"Plugin:"}),e.jsxs("select",{value:o,onChange:n=>d(n.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"All plugins"}),a.map(n=>e.jsx("option",{value:n.pluginSlug,children:n.displayName},n.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[P.size>0&&e.jsxs("button",{onClick:()=>D(!0),className:"flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm",children:[e.jsx($e,{className:"h-4 w-4 mr-1"}),"Delete (",P.size,")"]}),e.jsxs("button",{onClick:()=>U(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(Se,{className:"h-4 w-4 mr-1"}),"Add"]}),e.jsxs("button",{onClick:Je,disabled:j,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(se,{className:`h-4 w-4 mr-1 ${j?"animate-spin":""}`}),j?"Refreshing...":"Refresh Ranks"]})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsxs("h3",{className:"text-base font-semibold text-gray-900",children:["Keywords",o&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["for"," ",(je=a.find(n=>n.pluginSlug===o))==null?void 0:je.displayName]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"text-sm text-gray-700",children:["Showing ",Math.min(te,c.length)," of"," ",c.length," keywords"]}),te<c.length&&e.jsx("div",{className:"text-sm text-blue-600",children:"Scroll down to load more..."})]})]}),$?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(se,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading keywords..."})]}):c.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto max-h-[470px] overflow-y-auto",onScroll:ke,children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0 z-10",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12",children:e.jsx("input",{type:"checkbox",checked:c.slice(0,te).every(n=>P.has(n._id))&&c.length>0,onChange:n=>{const z=c.slice(0,te),q=new Set(P);n.target.checked?z.forEach(V=>q.add(V._id)):z.forEach(V=>q.delete(V._id)),F(q)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4",children:"Keyword"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Position"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Analytics"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Occurrences"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Tracked"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.slice(0,te).map((n,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-12",children:e.jsx("input",{type:"checkbox",checked:P.has(n._id),onChange:q=>{const V=new Set(P);q.target.checked?V.add(n._id):V.delete(n._id),F(V)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-1/4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:n.keyword})}),n.source&&e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${n.source==="manual"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:n.source})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:n.position||"-"}),n.rankChange!==null&&n.rankChange!==void 0&&e.jsxs("span",{className:`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${n.rankChange<0?"bg-green-100 text-green-800":n.rankChange>0?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:[n.rankChange<0?"↑":n.rankChange>0?"↓":"=",Math.abs(n.rankChange)]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm w-20",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:()=>Ge(n),className:"inline-flex items-center p-1.5 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors",title:"View Rank Analytics",children:e.jsx(we,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>Be(n),className:"inline-flex items-center p-1.5 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors",title:"Search Related Plugins",children:e.jsx(Hs,{className:"h-4 w-4"})})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-20",children:le[n._id]||0}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:n.addedAt?new Date(n.addedAt).toLocaleDateString("en-GB"):"-"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:(()=>{const q=n.lastChecked||n.updatedAt;if(!q)return"-";const V=new Date(q);return isNaN(V.getTime())?"-":V.toLocaleDateString("en-GB")})()})]},n._id))})]})}),ge&&e.jsxs("div",{className:"p-4 text-center",children:[e.jsx(se,{className:"h-5 w-5 text-blue-600 animate-spin mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Loading more keywords..."})]}),te>=c.length&&c.length>10&&e.jsx("div",{className:"p-4 text-center",children:e.jsx("p",{className:"text-sm text-gray-500",children:"All keywords loaded"})})]}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(fe,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Keywords Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:o?"No keywords added for this plugin yet.":"Please select a plugin first to view and manage keywords."}),o&&e.jsxs("button",{onClick:()=>U(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Se,{className:"h-4 w-4 mr-2"}),"Add First Keyword"]})]})]})]}),s==="competitors"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsx("h3",{className:"text-base font-semibold text-gray-900",children:"Competitor Plugins"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>de(!0),disabled:B,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(se,{className:`h-4 w-4 mr-1 ${B?"animate-spin":""}`}),B?"Discovering...":"Discover"]}),e.jsxs("button",{onClick:()=>Y(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(Se,{className:"h-4 w-4 mr-1"}),"Add Manually"]})]})]}),B?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(se,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading competitors..."})]}):E.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Slug"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Rank"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tags"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added Date"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:E.map((n,z)=>{var q;return e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:n.pluginName})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:n.pluginSlug})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:n.currentRank?`#${n.currentRank}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:((q=n.activeInstalls)==null?void 0:q.toLocaleString())||"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[n.tags&&n.tags.length>0?n.tags.slice(0,3).map((V,J)=>e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",children:V},`${n._id}-tag-${J}`)):e.jsx("span",{className:"text-gray-400",children:"No tags"}),n.tags&&n.tags.length>3&&e.jsxs("span",{className:"text-xs text-gray-500",children:["+",n.tags.length-3," more"]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:new Date(n.createdAt).toLocaleDateString("en-GB")})]},n._id)})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Re,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Competitors Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Add keywords to automatically discover competitor plugins, or add competitors manually."}),e.jsxs("button",{onClick:()=>Y(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Se,{className:"h-4 w-4 mr-2"}),"Add First Competitor"]})]})]}),e.jsx(he,{isOpen:y,onClose:()=>{U(!1),v(""),i("")},title:"Add New Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Plugin"}),e.jsxs("select",{value:f,onChange:n=>i(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Choose a plugin..."}),a.map(n=>e.jsx("option",{value:n.pluginSlug,children:n.displayName},n.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Keyword"}),e.jsx("input",{type:"text",value:S,onChange:n=>v(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter keyword...",onKeyDown:n=>{n.key==="Enter"&&H()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{U(!1),v(""),i("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:H,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Keyword"})]})]})}),e.jsx(he,{isOpen:T,onClose:()=>D(!1),title:"Delete Keywords",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-gray-600",children:["Are you sure you want to delete ",P.size," selected keyword(s)? This action cannot be undone and will also remove all related analytics data."]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>D(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Ye,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keywords"})]})]})}),e.jsx(he,{isOpen:C,onClose:()=>{L(!1),k(null)},title:"Delete Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-600",children:"Are you sure you want to delete this keyword? This action cannot be undone and will also remove all related analytics data."}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{L(!1),k(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Ue,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keyword"})]})]})}),e.jsx(he,{isOpen:Q,onClose:()=>{Y(!1),re("")},title:"Add Competitor Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsx("input",{type:"text",value:X,onChange:n=>re(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter plugin slug...",onKeyDown:n=>{n.key==="Enter"&&Fe()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{Y(!1),re("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Fe,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Competitor"})]})]})}),e.jsx(he,{isOpen:be,onClose:()=>{ue(!1),G(null),h([])},title:`Rank Analytics - ${(_==null?void 0:_.keyword)||""}`,children:e.jsxs("div",{className:"space-y-4",children:[I?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(se,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading rank history..."})]}):t.length>0?e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Rank Trend (Last 30 days)"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Plugin:"})," ",_==null?void 0:_.pluginName]})]}),e.jsx("div",{className:"h-64",children:e.jsx(Pe,{width:"100%",height:"100%",children:e.jsxs(es,{data:t.slice().reverse().map(n=>({...n,displayDate:n.date,invertedRank:n.rank?-n.rank:0})),margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Te,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(Ee,{dataKey:"displayDate",tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"}}),e.jsx(Le,{domain:["dataMin","dataMax"],tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"},tickFormatter:n=>`#${Math.abs(n)}`}),e.jsx(Ae,{contentStyle:{backgroundColor:"#ffffff",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"12px"},formatter:n=>[`#${Math.abs(n)}`,"Rank"],labelFormatter:n=>`Date: ${n}`}),e.jsx(ss,{type:"monotone",dataKey:"invertedRank",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2},children:e.jsx(_e,{dataKey:"invertedRank",position:"top",formatter:n=>`#${Math.abs(n)}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(zs,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Rank History"}),e.jsx("p",{className:"text-gray-600",children:"No rank history found for this keyword."})]}),e.jsx("div",{className:"flex justify-end pt-4",children:e.jsx("button",{onClick:()=>{ue(!1),G(null),h([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})}),e.jsx(he,{isOpen:pe,onClose:()=>{x(!1),G(null),m([])},title:`Related Plugins - "${(_==null?void 0:_.keyword)||""}"`,maxWidth:"max-w-6xl",fixedHeight:!0,children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx("div",{className:"flex-1 overflow-y-auto",children:W?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(se,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading related plugins..."})]}):u.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rating"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tested Up To"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Update"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:u.map((n,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap",children:[e.jsxs("a",{href:n.wordpressUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 font-medium flex items-center",title:js(n.pluginName),children:[jt(n.pluginName),e.jsx(Ke,{className:"h-3 w-3 ml-1"})]}),e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:n.pluginSlug})]}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:n.activeInstalls>0?n.activeInstalls.toLocaleString():"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ze,{className:"h-4 w-4 text-yellow-400 mr-1"}),e.jsxs("span",{children:[n.rating>0?n.rating.toFixed(1):"N/A",n.numRatings>0&&e.jsxs("span",{className:"text-xs text-gray-400 ml-1",children:["(",n.numRatings,")"]})]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:n.testedUpTo!=="N/A"?`WP ${n.testedUpTo}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:n.lastUpdated!=="N/A"?(()=>{const q=new Date(n.lastUpdated);if(isNaN(q.getTime()))return"N/A";const V=q.getDate().toString().padStart(2,"0"),J=(q.getMonth()+1).toString().padStart(2,"0"),ae=q.getFullYear();return`${J}-${V}-${ae}`})():"N/A"})]},n.pluginSlug))})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(fe,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Related Plugins Found"}),e.jsx("p",{className:"text-gray-600",children:"No plugins found containing this keyword."})]})}),e.jsx("div",{className:"flex justify-end pt-4 border-t border-gray-200 flex-shrink-0",children:e.jsx("button",{onClick:()=>{x(!1),G(null),m([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})})]})},Nt=()=>{const[s,l]=r.useState("downloaded-data"),[a,b]=r.useState([]),[o,d]=r.useState(!1),[c,w]=r.useState(""),[$,N]=r.useState([]),[y,U]=r.useState(!1),[S,v]=r.useState("15"),[f,i]=r.useState({start:"",end:""}),[j,R]=r.useState(""),[P,F]=r.useState([]),[T,D]=r.useState(!1),[A,g]=r.useState({startDate:"",endDate:"",rating:[],page:1}),[p,k]=r.useState({totalReviews:0,averageRating:0,ratingDistribution:{}});r.useEffect(()=>{C()},[]);const C=async()=>{try{d(!0);const E=localStorage.getItem("adminToken"),B=await fetch("https://pluginsight.vercel.app/api/analytics/added-plugins",{headers:{Authorization:`Bearer ${E}`,"Content-Type":"application/json"}});if(!B.ok)throw new Error(`HTTP error! status: ${B.status}`);const K=await B.json();K.success?b(K.plugins):console.warn("Failed to load added plugins:",K.message)}catch(E){console.error("Error loading added plugins:",E),E.name==="TypeError"&&E.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins","error")}finally{d(!1)}},L=[{id:"downloaded-data",name:"Downloaded Data",icon:me},{id:"plugin-reviews",name:`Plugin Reviews${p.totalReviews>0?` (${p.totalReviews})`:""}`,icon:Qe}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(is,{className:"h-6 w-6 text-blue-600 mr-2"}),"Plugin Data Analysis"]})})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8 px-6",children:L.map(E=>{const O=E.icon;return e.jsxs("button",{onClick:()=>l(E.id),className:`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${s===E.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(O,{className:"h-4 w-4"}),e.jsx("span",{children:E.name})]},E.id)})})}),e.jsxs("div",{className:"p-6",children:[s==="downloaded-data"&&e.jsx(vt,{addedPlugins:a,selectedPlugin:c,setSelectedPlugin:w,downloadData:$,setDownloadData:N,downloadLoading:y,setDownloadLoading:U,dateRange:S,setDateRange:v,customDateRange:f,setCustomDateRange:i}),s==="plugin-reviews"&&e.jsx(kt,{addedPlugins:a,reviewsPlugin:j,setReviewsPlugin:R,reviews:P,setReviews:F,reviewsLoading:T,setReviewsLoading:D,reviewFilters:A,setReviewFilters:g,reviewStats:p,setReviewStats:k})]})]})]})},vt=({addedPlugins:s,selectedPlugin:l,setSelectedPlugin:a,downloadData:b,setDownloadData:o,downloadLoading:d,setDownloadLoading:c,dateRange:w,setDateRange:$,customDateRange:N,setCustomDateRange:y})=>{var v;const U=async()=>{try{c(!0),window.toast("Starting plugin download data fetch...","info");const f=localStorage.getItem("adminToken"),R=await(await fetch("https://pluginsight.vercel.app/api/analytics/download-data/refresh",{method:"POST",headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}})).json();R.success?(window.toast(R.message,"success"),l&&S(l)):window.toast(R.message||"Failed to refresh download data","error")}catch(f){console.error("Error refreshing download data:",f),window.toast("Failed to refresh download data","error")}finally{c(!1)}},S=async f=>{if(f)try{c(!0);const i=localStorage.getItem("adminToken"),j="https://pluginsight.vercel.app";let R=`${j}/api/analytics/download-data/${f}?days=${w}`;w==="custom"&&N.start&&N.end&&(R=`${j}/api/analytics/download-data/${f}?startDate=${N.start}&endDate=${N.end}`);const F=await(await fetch(R,{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).json();if(F.success){const T=F.downloadData.map(D=>({date:new Date(D.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:D.downloads,fullDate:D.date}));o(T)}}catch(i){console.error("Error loading download data:",i),window.toast("Failed to load download data","error")}finally{c(!1)}};return Xe.useEffect(()=>{l&&S(l)},[l,w,N]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:l,onChange:f=>a(f.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(f=>e.jsx("option",{value:f.pluginSlug,children:f.displayName||f.pluginName},f.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:w,onChange:f=>$(f.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom Range"})]})]}),w==="custom"&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:N.start,onChange:f=>y(i=>({...i,start:f.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:N.end,onChange:f=>y(i=>({...i,end:f.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsxs("button",{onClick:U,disabled:d,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(se,{className:`h-4 w-4 mr-2 ${d?"animate-spin":""}`}),"Refresh"]})]}),l&&b.length>0?e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Download Trends -"," ",((v=s.find(f=>f.pluginSlug===l))==null?void 0:v.displayName)||l]}),e.jsx("div",{className:"h-96",children:e.jsx(Pe,{width:"100%",height:"100%",children:e.jsxs(xs,{data:b,children:[e.jsx(Te,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(Ee,{dataKey:"date",stroke:"#6b7280",fontSize:12}),e.jsx(Le,{stroke:"#6b7280",fontSize:12,tickFormatter:f=>f.toLocaleString()}),e.jsx(Ae,{contentStyle:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"8px"},formatter:f=>[f.toLocaleString(),"Downloads"],labelFormatter:(f,i)=>i&&i[0]?new Date(i[0].payload.fullDate).toLocaleDateString("en-GB",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):f}),e.jsx(hs,{dataKey:"downloads",fill:"#3b82f6",radius:[4,4,0,0],children:e.jsx(_e,{dataKey:"downloads",position:"top",fontSize:10,fill:"#3b82f6",formatter:f=>f.toLocaleString()})})]})})})]}):l&&d?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(se,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading download data..."})]})}):l?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(me,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Download Data"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No download data found for this plugin. Click refresh to fetch the latest data."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(me,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its download trends."})]})})]})},kt=({addedPlugins:s,reviewsPlugin:l,setReviewsPlugin:a,reviews:b,setReviews:o,reviewsLoading:d,setReviewsLoading:c,reviewFilters:w,setReviewFilters:$,reviewStats:N,setReviewStats:y})=>{const U=async()=>{try{c(!0),window.toast("Starting plugin reviews fetch...","info");const i=localStorage.getItem("adminToken"),R=await fetch("https://pluginsight.vercel.app/api/analytics/reviews/refresh",{method:"POST",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}});if(!R.ok)throw new Error(`HTTP error! status: ${R.status}`);const P=await R.json();P.success?(window.toast(P.message,"success"),l&&S(l)):window.toast(P.message||"Failed to refresh reviews","error")}catch(i){console.error("Error refreshing reviews:",i),i.name==="TypeError"&&i.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to refresh reviews","error")}finally{c(!1)}},S=async(i,j=1)=>{var R;if(i)try{c(!0);const P=new URLSearchParams({page:j.toString(),limit:"20"});w.startDate&&P.append("startDate",w.startDate),w.endDate&&P.append("endDate",w.endDate),w.rating.length>0&&w.rating.forEach(D=>P.append("rating",D.toString())),console.log(`Loading reviews for plugin: ${i} with params:`,P.toString());const F=await He(`/api/analytics/reviews/${i}?${P}`);if(!F.ok)throw new Error(`HTTP error! status: ${F.status}`);const T=await F.json();console.log("Reviews API response:",T),T.success?(console.log(`Loaded ${((R=T.reviews)==null?void 0:R.length)||0} reviews for ${i} (page ${j})`),o(j===1?T.reviews||[]:D=>[...D,...T.reviews||[]]),y(T.stats||{})):(console.warn("Reviews API returned success: false",T),j===1&&o([]),y({}))}catch(P){console.error("Error loading reviews:",P),P.name==="TypeError"&&P.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast(`Failed to load reviews: ${P.message}`,"error"),j===1&&(o([]),y({}))}finally{c(!1)}};Xe.useEffect(()=>{l&&S(l)},[l,w]);const v=i=>Array.from({length:5},(j,R)=>e.jsx("span",{className:`text-lg ${R<i?"text-yellow-400":"text-gray-300"}`,children:"★"},R)),f=i=>{if(!i)return"";let j=i.replace(/<!\[CDATA\[/g,"").replace(/\]\]>/g,"").trim();j=j.replace(/^.*?Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/^.*?Replies:\s*\d+\s*/gi,"").replace(/^.*?Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*/gi,"").replace(/Rating:\s*\d+\s*stars?\s*/gi,"").trim();const R=document.createElement("textarea");return R.innerHTML=j,j=R.value,j=j.replace(/<[^>]*>/g,""),j=j.replace(/\s+/g," ").trim(),j};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:l,onChange:i=>a(i.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(i=>e.jsx("option",{value:i.pluginSlug,children:i.displayName||i.pluginName},i.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:w.startDate,onChange:i=>$(j=>({...j,startDate:i.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:w.endDate,onChange:i=>$(j=>({...j,endDate:i.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rating Filter"}),e.jsxs("select",{value:w.rating.length===1?w.rating[0]:"",onChange:i=>{const j=i.target.value;$(j===""?R=>({...R,rating:[]}):R=>({...R,rating:[parseInt(j)]}))},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"All ratings"}),e.jsx("option",{value:"5",children:"5 stars"}),e.jsx("option",{value:"4",children:"4 stars"}),e.jsx("option",{value:"3",children:"3 stars"}),e.jsx("option",{value:"2",children:"2 stars"}),e.jsx("option",{value:"1",children:"1 star"})]})]})]}),e.jsxs("button",{onClick:U,disabled:d,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(se,{className:`h-4 w-4 mr-2 ${d?"animate-spin":""}`}),"Refresh"]})]}),l&&b.length>0?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"text-sm text-gray-600 flex items-center gap-2",children:["Total",e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:N.totalReviews}),"Reviews"]})})})}),e.jsx("div",{className:"h-[460px] overflow-y-auto space-y-4",children:b.map((i,j)=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 animate-fade-in-up",style:{animationDelay:`${j*100}ms`,animationFillMode:"both"},children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex",children:v(i.rating)}),e.jsxs("div",{className:"text-sm text-gray-500",children:["by ",i.author," •"," ",new Date(i.date).toLocaleDateString("en-GB")]})]}),i.reviewUrl&&e.jsx("a",{href:i.reviewUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-blue-500 rounded-lg hover:text-blue-700 transition-all duration-200",children:e.jsx(Ke,{className:"h-4 w-4"})})]}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:i.title}),e.jsx("div",{className:"text-gray-700 leading-relaxed",children:f(i.content)})]},i._id||j))})]}):l&&d?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(se,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):l?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Qe,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Reviews Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No reviews found for this plugin. Click refresh to fetch the latest reviews."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Qe,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its reviews."})]})})]})},St=()=>{const{user:s}=ye(),[l,a]=r.useState([]),[b,o]=r.useState(!0),[d,c]=r.useState(""),[w,$]=r.useState(""),[N,y]=r.useState(""),[U,S]=r.useState(!1),[v,f]=r.useState(!1),[i,j]=r.useState(!1),[R,P]=r.useState(!1),[F,T]=r.useState(!1),[D,A]=r.useState(null),[g,p]=r.useState({current:1,pages:1,total:0,limit:10}),[k,C]=r.useState({name:"",email:"",password:"",role:"member"}),[L,E]=r.useState({newPassword:"",confirmPassword:""}),[O,B]=r.useState({canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1}),K=async(x=1,_="",G="")=>{try{o(!0);const t=localStorage.getItem("adminToken");if(!t){c("Authentication token not found. Please login again.");return}const h=new URLSearchParams({page:x.toString(),limit:"10",..._&&{search:_},...G&&{role:G}}),m=await fetch(`https://pluginsight.vercel.app/api/users?${h}`,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!m.ok){if(m.status===401){c("Authentication failed. Please login again."),localStorage.removeItem("adminToken");return}throw new Error(`HTTP error! status: ${m.status}`)}const I=await m.json();I.success?(a(I.users),p(I.pagination),c("")):c(I.message||"Failed to fetch users")}catch(t){console.error("Fetch users error:",t),t.name==="TypeError"&&t.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to fetch users. Please try again.")}finally{o(!1)}};r.useEffect(()=>{K()},[]);const Q=()=>{K(1,w,N)},Y=async x=>{x.preventDefault();try{const _=localStorage.getItem("adminToken"),t=await fetch("https://pluginsight.vercel.app/api/users",{method:"POST",headers:{Authorization:`Bearer ${_}`,"Content-Type":"application/json"},body:JSON.stringify(k)});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const h=await t.json();h.success?(S(!1),C({name:"",email:"",password:"",role:"member"}),K(g.current,w,N)):c(h.message||"Failed to create user")}catch(_){console.error("Add user error:",_),_.name==="TypeError"&&_.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to create user")}},X=async x=>{x.preventDefault();try{const _=localStorage.getItem("adminToken"),t=await fetch(`https://pluginsight.vercel.app/api/users/${D._id}`,{method:"PUT",headers:{Authorization:`Bearer ${_}`,"Content-Type":"application/json"},body:JSON.stringify({name:k.name,email:k.email,role:k.role,isActive:k.isActive})});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const h=await t.json();h.success?(f(!1),A(null),K(g.current,w,N)):c(h.message||"Failed to update user")}catch(_){console.error("Edit user error:",_),_.name==="TypeError"&&_.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to update user")}},re=x=>{if(s&&s._id===x._id){window.toast("You cannot delete your own account","error");return}A(x),P(!0)},le=async()=>{if(D)try{const x=localStorage.getItem("adminToken"),G=await fetch(`https://pluginsight.vercel.app/api/users/${D._id}`,{method:"DELETE",headers:{Authorization:`Bearer ${x}`,"Content-Type":"application/json"}});if(!G.ok)throw new Error(`HTTP error! status: ${G.status}`);const t=await G.json();t.success?(K(g.current,w,N),P(!1),A(null)):c(t.message||"Failed to delete user")}catch(x){console.error("Delete user error:",x),x.name==="TypeError"&&x.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to delete user")}},ne=async x=>{if(x.preventDefault(),L.newPassword!==L.confirmPassword){c("Passwords do not match");return}try{const _=localStorage.getItem("adminToken"),t=await fetch(`https://pluginsight.vercel.app/api/users/${D._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${_}`,"Content-Type":"application/json"},body:JSON.stringify({newPassword:L.newPassword})});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const h=await t.json();h.success?(j(!1),A(null),E({newPassword:"",confirmPassword:""}),alert("Password reset successfully")):c(h.message||"Failed to reset password")}catch(_){console.error("Reset password error:",_),_.name==="TypeError"&&_.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to reset password")}},be=x=>{A(x),C({name:x.name,email:x.email,role:x.role,isActive:x.isActive}),f(!0)},ue=x=>{A(x),E({newPassword:"",confirmPassword:""}),j(!0)},te=x=>{var _,G,t,h;A(x),B({canAddPlugins:((_=x.permissions)==null?void 0:_.canAddPlugins)||!1,canDeletePlugins:((G=x.permissions)==null?void 0:G.canDeletePlugins)||!1,canAddKeywords:((t=x.permissions)==null?void 0:t.canAddKeywords)||!1,canAddUsers:((h=x.permissions)==null?void 0:h.canAddUsers)||!1}),T(!0)},ce=x=>{switch(x){case"superadmin":return"bg-red-100 text-red-800";case"admin":return"bg-blue-100 text-blue-800";case"member":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},ge=x=>s.role==="superadmin"||s.role==="admin"&&x.role==="member"?s._id!==x._id:!1,oe=x=>!(s.role==="admin"&&x.role==="superadmin"),pe=x=>s._id===x._id?!1:s.role==="superadmin"||s.role==="admin"&&x.role==="member";return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center p-4",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Ks,{className:"h-6 w-6 text-blue-600 mr-2"}),"Team Members"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage system users and their permissions"})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200 flex justify-between items-center",children:[e.jsx("div",{children:["admin","superadmin"].includes(s==null?void 0:s.role)&&e.jsxs("button",{onClick:()=>S(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Add User"]})}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"flex relative",children:[e.jsx(fe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:w,onChange:x=>$(x.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Vs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsxs("select",{value:N,onChange:x=>y(x.target.value),className:"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Roles"}),e.jsx("option",{value:"superadmin",children:"Super Admin"}),e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"})]})]}),e.jsx("button",{onClick:Q,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Search"})]})]}),d&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:d}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:b?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Loading users..."})]}):l.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Re,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:l.filter(oe).map(x=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:x.name}),e.jsx("div",{className:"text-sm text-gray-500",children:x.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${ce(x.role)}`,children:x.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${x.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:x.isActive?"Active":"Inactive"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(x.createdAt).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx("div",{className:"flex items-center gap-2",children:ge(x)&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>be(x),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:e.jsx(ms,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>ue(x),className:"text-yellow-600 hover:text-yellow-900",title:"Reset Password",children:e.jsx(qs,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>te(x),className:"text-purple-600 hover:text-purple-900",title:"Manage Permissions",children:e.jsx(Ze,{className:"h-4 w-4"})}),pe(x)&&e.jsx("button",{onClick:()=>re(x),className:"text-red-600 hover:text-red-900",title:"Delete User",children:e.jsx($e,{className:"h-4 w-4"})})]})})})]},x._id))})]})})}),g.pages>1&&e.jsxs("div",{className:"flex justify-center items-center gap-2",children:[e.jsx("button",{onClick:()=>K(g.current-1,w,N),disabled:g.current===1,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Previous"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",g.current," of ",g.pages]}),e.jsx("button",{onClick:()=>K(g.current+1,w,N),disabled:g.current===g.pages,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Next"})]}),U&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Add New User"}),e.jsxs("form",{onSubmit:Y,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:k.name,onChange:x=>C({...k,name:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:k.email,onChange:x=>C({...k,email:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{type:"password",value:k.password,onChange:x=>C({...k,password:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:k.role,onChange:x=>C({...k,role:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{S(!1),C({name:"",email:"",password:"",role:"member"})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Add User"})]})]})]})}),v&&D&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Edit User"}),e.jsxs("form",{onSubmit:X,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:k.name,onChange:x=>C({...k,name:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:k.email,onChange:x=>C({...k,email:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:k.role,onChange:x=>C({...k,role:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:(s==null?void 0:s.role)==="admin"&&["admin","superadmin"].includes(D.role),children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsx("div",{children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:k.isActive,onChange:x=>C({...k,isActive:x.target.checked}),className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{f(!1),A(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Update User"})]})]})]})}),i&&D&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Reset Password"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["Reset password for: ",e.jsx("strong",{children:D.name})]}),e.jsxs("form",{onSubmit:ne,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),e.jsx("input",{type:"password",value:L.newPassword,onChange:x=>E({...L,newPassword:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",value:L.confirmPassword,onChange:x=>E({...L,confirmPassword:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{j(!1),A(null),E({newPassword:"",confirmPassword:""})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:"Reset Password"})]})]})]})}),R&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(Ws,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Delete User"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this user? This action cannot be undone."})]})]}),D&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:D.name}),e.jsx("p",{className:"text-sm text-gray-500",children:D.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${ce(D.role)}`,children:D.role})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{P(!1),A(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:le,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete User"})]})]})}),e.jsx(he,{isOpen:F,onClose:()=>{T(!1),A(null)},title:"Manage User Permissions",children:D&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:D.name}),e.jsx("p",{className:"text-sm text-gray-500",children:D.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${ce(D.role)}`,children:D.role})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Plugins"}),e.jsx("input",{type:"checkbox",checked:O.canAddPlugins,onChange:x=>B(_=>({..._,canAddPlugins:x.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Delete Plugins"}),e.jsx("input",{type:"checkbox",checked:O.canDeletePlugins,onChange:x=>B(_=>({..._,canDeletePlugins:x.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Keywords"}),e.jsx("input",{type:"checkbox",checked:O.canAddKeywords,onChange:x=>B(_=>({..._,canAddKeywords:x.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Users"}),e.jsx("input",{type:"checkbox",checked:O.canAddUsers,onChange:x=>B(_=>({..._,canAddUsers:x.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{onClick:()=>{T(!1),A(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:()=>{window.toast("Permissions updated successfully","success"),T(!1),A(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Save Permissions"})]})]})})]})},Pt=()=>{const{user:s}=ye(),[l,a]=r.useState(!1),[b,o]=r.useState(""),[d,c]=r.useState(""),[w,$]=r.useState({member:{canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1},admin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0},superadmin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0}});r.useEffect(()=>{N()},[]);const N=async()=>{try{const i=localStorage.getItem("adminToken"),P=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).json();P.success&&$(P.permissions)}catch(i){console.error("Error loading permissions:",i)}},y=(i,j,R)=>{$(P=>({...P,[i]:{...P[i],[j]:R}}))},U=async()=>{a(!0),c(""),o("");try{const i=localStorage.getItem("adminToken"),P=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify({permissions:w})})).json();P.success?(o("Permissions updated successfully"),setTimeout(()=>o(""),3e3)):c(P.message||"Failed to update permissions")}catch(i){console.error("Error saving permissions:",i),c("Failed to update permissions")}finally{a(!1)}},S={canAddPlugins:"Add Plugins",canDeletePlugins:"Delete Plugins",canAddKeywords:"Add Keywords",canAddUsers:"Add Users"},v={member:"Member",admin:"Admin",superadmin:"Super Admin"};if(!s)return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-white rounded-lg p-8 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Oe,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600",children:"Please login to access settings."})]})})});const f=["admin","superadmin"].includes(s.role);return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Ze,{className:"h-8 w-8 text-blue-600 mr-3"}),"Settings"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Manage application settings and user permissions"})]})})}),b&&e.jsx("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg",children:b}),d&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:d}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 w-1/2",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(Re,{className:"h-5 w-5 text-gray-600 mr-2"}),"User Permissions"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Configure what actions each user role can perform"})]}),e.jsx("div",{className:"flex justify-end",children:f?e.jsxs("button",{onClick:U,disabled:l,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[e.jsx(us,{className:"h-4 w-4 mr-2"}),l?"Saving...":"Save"]}):e.jsx("div",{className:"text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-lg",children:"View Only - Admin privileges required to edit"})})]}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Permission"}),Object.keys(v).map(i=>e.jsx("th",{className:"text-center py-3 px-4 font-medium text-gray-900",children:v[i]},i))]})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:Object.keys(S).map(i=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"py-4 px-4 text-sm font-medium text-gray-900",children:S[i]}),Object.keys(v).map(j=>{var R;return e.jsx("td",{className:"py-4 px-4 text-center",children:e.jsx("input",{type:"checkbox",checked:((R=w[j])==null?void 0:R[i])||!1,onChange:P=>y(j,i,P.target.checked),disabled:!f||j==="superadmin",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"})},j)})]},i))})]})})})]})]})},At=()=>{const{user:s}=ye(),[l,a]=r.useState(!1),[b,o]=r.useState(!1),[d,c]=r.useState({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),w=async S=>{S.preventDefault(),o(!0);try{if(d.newPassword){if(d.newPassword!==d.confirmPassword){window.toast("New passwords do not match","error"),o(!1);return}if(d.newPassword.length<6){window.toast("Password must be at least 6 characters","error"),o(!1);return}}const v=localStorage.getItem("adminToken"),f="https://pluginsight.vercel.app",i={name:d.name,email:d.email},j=await fetch(`${f}/api/users/${s.id||s._id}`,{method:"PUT",headers:{Authorization:`Bearer ${v}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!j.ok)throw new Error(`HTTP error! status: ${j.status}`);const R=await j.json();if(!R.success){window.toast(R.message||"Failed to update profile","error"),o(!1);return}if(d.newPassword){const F={newPassword:d.newPassword},T=await fetch(`${f}/api/users/${s.id||s._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${v}`,"Content-Type":"application/json"},body:JSON.stringify(F)});if(!T.ok)throw new Error(`HTTP error! status: ${T.status}`);const D=await T.json();if(!D.success){window.toast(D.message||"Failed to update password","error"),o(!1);return}}const P={...s,...R.user};localStorage.setItem("adminUser",JSON.stringify(P)),window.toast("Profile updated successfully","success"),a(!1),c(F=>({...F,name:P.name,email:P.email,newPassword:"",confirmPassword:""})),window.location.reload()}catch(v){console.error("Error updating profile:",v),v.name==="TypeError"&&v.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to update profile","error")}finally{o(!1)}},$=()=>{c({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),a(!1)},N=S=>{c({...d,[S.target.name]:S.target.value})},y=S=>{switch(S){case"superadmin":return e.jsx(Oe,{className:"h-5 w-5 text-yellow-600"});case"admin":return e.jsx(Oe,{className:"h-5 w-5 text-blue-600"});default:return e.jsx(De,{className:"h-5 w-5 text-gray-600"})}},U=S=>{const v={superadmin:"bg-yellow-100 text-yellow-800 border-yellow-200",admin:"bg-blue-100 text-blue-800 border-blue-200",member:"bg-gray-100 text-gray-800 border-gray-200"};return e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${v[S]}`,children:[y(S),e.jsx("span",{className:"ml-2 capitalize",children:S})]})};return s?e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Profile Settings"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your account information and preferences"})]}),e.jsx("div",{className:"",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"h-24 w-24 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4",children:e.jsx(De,{className:"h-12 w-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:s==null?void 0:s.name}),e.jsx("p",{className:"text-blue-100 mb-4",children:s==null?void 0:s.email}),U(s==null?void 0:s.role)]})})}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Personal Information"}),l?e.jsxs("button",{onClick:$,className:"flex items-center space-x-2 px-4 py-2 bg-gray-50 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:[e.jsx(qe,{className:"h-4 w-4"}),e.jsx("span",{children:"Cancel"})]}):e.jsxs("button",{onClick:()=>a(!0),className:"flex items-center space-x-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",children:[e.jsx(ms,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]}),e.jsxs("form",{onSubmit:w,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(De,{className:"h-4 w-4 mr-2"}),"Full Name"]}),e.jsx("input",{type:"text",name:"name",value:d.name,onChange:N,disabled:!l,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(cs,{className:"h-4 w-4 mr-2"}),"Email Address"]}),e.jsx("input",{type:"email",name:"email",value:d.email,onChange:N,disabled:!l,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Oe,{className:"h-4 w-4 mr-2"}),"Role"]}),e.jsx("div",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500",children:e.jsx("span",{className:"capitalize",children:s==null?void 0:s.role})}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Role can only be changed by administrators"})]}),l&&e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),e.jsx("input",{type:"password",name:"newPassword",value:d.newPassword,onChange:N,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter new password"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),e.jsx("input",{type:"password",name:"confirmPassword",value:d.confirmPassword,onChange:N,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Confirm new password"})]})]})]}),l&&e.jsx("div",{className:"flex justify-end pt-6",children:e.jsxs("button",{type:"submit",disabled:b,className:"flex items-center space-x-2 px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(us,{className:"h-4 w-4"}),e.jsx("span",{children:b?"Saving...":"Save"})]})})]})]})})]})})})]})}):e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading profile..."})]})})},Ct=["p","br","strong","b","em","i","u","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","pre","code","a","img","div","span","table","thead","tbody","tr","td","th","iframe","video","source"],Dt={a:["href","title","target","rel"],img:["src","alt","title","width","height","class"],iframe:["src","width","height","frameborder","allowfullscreen","title","class"],video:["src","width","height","controls","autoplay","muted","loop","poster","class"],source:["src","type"],div:["class","id"],span:["class","id"],p:["class"],h1:["class"],h2:["class"],h3:["class"],h4:["class"],h5:["class"],h6:["class"],ul:["class"],ol:["class"],li:["class"],table:["class"],thead:["class"],tbody:["class"],tr:["class"],td:["class"],th:["class"],blockquote:["class"],pre:["class"],code:["class"]};function Rt(s){if(!s)return"";const l=document.createElement("div");l.innerHTML=s;function a(o){const d=o.tagName.toLowerCase();if(!Ct.includes(d)){o.remove();return}if(d==="iframe"){const N=o.getAttribute("src"),y=o.getAttribute("title")||"Video content",U=document.createElement("div");U.className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center my-4";let S="Video",v=`<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
      </svg>`;N&&N.includes("youtube")?(S="YouTube Video",v=`<svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
        </svg>`):N&&N.includes("vimeo")&&(S="Vimeo Video",v=`<svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.977 6.416c-.105 2.338-1.739 5.543-4.894 9.609-3.268 4.247-6.026 6.37-8.29 6.37-1.409 0-2.578-1.294-3.553-3.881L5.322 11.4C4.603 8.816 3.834 7.522 3.01 7.522c-.179 0-.806.378-1.881 1.132L0 7.197a315.065 315.065 0 0 0 4.192-3.729C5.978 2.4 7.333 1.718 8.222 1.718c2.104 0 3.391 1.262 3.863 3.783.508 2.27.861 3.683.861 4.235 0 1.288-.547 3.2-1.642 5.737-.832 1.96-1.747 2.94-2.747 2.94-.842 0-1.638-.79-2.387-2.37l-.318-.81c-.613-1.96-1.17-2.94-1.668-2.94-.498 0-1.225.562-2.178 1.688l-.951-1.4c1.588-1.96 3.176-2.94 4.764-2.94 1.588 0 2.823 1.225 3.706 3.676.883 2.45 1.225 3.676 1.225 3.676s.342 1.96 1.026 5.88c.684 3.92 1.026 5.88 1.026 5.88.342 1.96 1.026 2.94 2.052 2.94 1.026 0 2.394-.98 4.104-2.94 1.71-1.96 2.565-3.92 2.565-5.88z"/>
        </svg>`),U.innerHTML=`
        <div class="flex flex-col items-center space-y-3">
          ${v}
          <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-1">${S}</h4>
            <p class="text-gray-600 text-sm mb-3">${y}</p>
            <a href="${N}" target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Watch Video
            </a>
          </div>
        </div>
      `,o.parentNode.replaceChild(U,o);return}const c=Dt[d]||[];Array.from(o.attributes).forEach(N=>{c.includes(N.name)||o.removeAttribute(N.name)}),Array.from(o.children).forEach(N=>a(N))}return Array.from(l.children).forEach(o=>a(o)),l.innerHTML}function Me(s){if(!s)return"";let l=s.replace(/\[video\s+([^\]]+)\]/g,(a,b)=>{const o=b.match(/src="([^"]+)"/);return o?`<video controls><source src="${o[1]}" type="video/mp4"></video>`:""}).replace(/\[youtube\s+([^\]]+)\]/g,(a,b)=>{const o=b.match(/(?:id="|v=)([^"&\s]+)/);return o?`<iframe src="https://www.youtube.com/embed/${o[1]}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`:""}).replace(/\[vimeo\s+([^\]]+)\]/g,(a,b)=>{const o=b.match(/id="?([^"\s]+)"?/);return o?`<iframe src="https://player.vimeo.com/video/${o[1]}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`:""}).replace(/https?:\/\/(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/g,(a,b)=>`<iframe src="https://www.youtube.com/embed/${b}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g,(a,b)=>`<iframe src="https://www.youtube.com/embed/${b}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/(?:www\.)?vimeo\.com\/(\d+)/g,(a,b)=>`<iframe src="https://player.vimeo.com/video/${b}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`);return Rt(l)}const $t=()=>{const{slug:s}=Ss(),l=Ve(),[a,b]=r.useState(null),[o,d]=r.useState(null),[c,w]=r.useState(!0),[$,N]=r.useState(""),[y,U]=r.useState({}),[S,v]=r.useState(""),[f,i]=r.useState(!0),[j,R]=r.useState(!1),P=async()=>{try{i(!0);const p=await fetch(`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&slug=${a.slug}`);if(!p.ok)throw new Error("Failed to fetch plugin information");const k=await p.json();k.versions&&U(k.versions)}catch(p){console.error("Error fetching versions data:",p),U({})}finally{i(!1)}};r.useEffect(()=>{a&&P()},[a]),r.useEffect(()=>{F()},[s]);const F=async()=>{try{w(!0);const p=localStorage.getItem("adminToken"),k="https://pluginsight.vercel.app",[C,L]=await Promise.all([fetch(`${k}/api/plugins/${s}`,{headers:{Authorization:`Bearer ${p}`}}),fetch(`${k}/api/analytics/plugin-info/${s}`,{headers:{Authorization:`Bearer ${p}`}})]);if(!C.ok)throw new Error("Failed to fetch plugin details");const E=await C.json();if(!E.success){N(E.message||"Plugin not found");return}if(b(E.plugin),L.ok){const O=await L.json();O.success&&O.pluginInfo?d(O.pluginInfo):console.log("No plugin information found in database for:",s)}else console.log("Failed to fetch plugin information from database")}catch(p){console.error("Error fetching plugin details:",p),N("Failed to load plugin details")}finally{w(!1)}},T=p=>{if(!p)return"N/A";const k=p.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!k)return"N/A";const[,C,L,E]=k;return`${E}-${L}-${C}`},D=p=>{const k=Math.round((p||0)/20);return[...Array(5)].map((C,L)=>e.jsx(ze,{className:`h-4 w-4 ${L<k?"text-yellow-400 fill-current":"text-gray-300"}`},L))},A=p=>o&&o[p]!==void 0&&o[p]!==null?o[p]:a!=null&&a.pluginData&&a.pluginData[p]!==void 0&&a.pluginData[p]!==null?a.pluginData[p]:null,g=p=>{const k=p.split(".");if(o){let C=o;for(const L of k)if(C&&typeof C=="object"&&C[L]!==void 0)C=C[L];else{C=null;break}if(C!==null)return C}if(a!=null&&a.pluginData){let C=a.pluginData;for(const L of k)if(C&&typeof C=="object"&&C[L]!==void 0)C=C[L];else{C=null;break}return C}return null};return c?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):$||!a?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-red-600 text-xl mb-4",children:$}),e.jsx("button",{onClick:()=>l(-1),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Go Back"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("button",{onClick:()=>l(-1),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[e.jsx(Js,{className:"h-5 w-5"}),e.jsx("span",{children:"Back"})]}),e.jsx("div",{className:"h-6 w-px bg-gray-300"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Plugin Details"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[A("homepage")&&e.jsxs("button",{onClick:()=>window.open(A("homepage"),"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(Ke,{className:"h-4 w-4"}),e.jsx("span",{children:"Home page"})]}),e.jsxs("button",{onClick:()=>window.open(`https://wordpress.org/plugins/${a.slug}/`,"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(Ke,{className:"h-4 w-4"}),e.jsx("span",{children:"View on WordPress.org"})]})]})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[g("icons.2x")&&e.jsx("img",{src:g("icons.2x"),alt:a.name,className:"w-16 h-16 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:a.name}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[D(A("rating")),e.jsxs("span",{className:"text-sm text-gray-600 ml-2",children:["(",A("num_ratings")||0," ratings)"]})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[e.jsx(me,{className:"h-4 w-4"}),e.jsxs("span",{children:[(A("downloaded")||0).toLocaleString()," ","downloads"]})]})]})]})]})}),g("sections.description")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Description"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Me(g("sections.description"))}})]}),g("sections.installation")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Installation"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Me(g("sections.installation"))}})]}),g("sections.faq")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Frequently Asked Questions"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Me(g("sections.faq"))}})]}),g("sections.changelog")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Changelog"}),e.jsx("div",{className:"prose max-w-none text-gray-700 max-h-96 overflow-y-auto",dangerouslySetInnerHTML:{__html:Me(g("sections.changelog"))}})]}),A("screenshots")&&Object.keys(A("screenshots")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Screenshots"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(A("screenshots")).slice(0,6).map(([p,k])=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("img",{src:k.src,alt:k.caption||`Screenshot ${p}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200",loading:"lazy"}),k.caption&&e.jsx("p",{className:"text-sm text-gray-600",children:k.caption})]},p))})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plugin Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("version")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Rank"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",a.currentRank||"N/A"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:T(A("last_updated"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Added"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:T(A("added"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Requires WP"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("requires")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Tested up to"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("tested")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"PHP Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("requires_php")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Active Installs"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("active_installs")?`${A("active_installs").toLocaleString()}+`:"N/A"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Download"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 flex gap-1 items-center",children:["Current Version",e.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Latest"})]}),e.jsxs("div",{className:"text-md font-bold text-gray-900",children:["v",A("version")||"N/A"]}),e.jsx("button",{onClick:()=>window.open(A("download_link"),"_blank"),className:"bg-green-600 hover:bg-green-700 text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:e.jsx(me,{className:"h-4 w-4"})})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Older Versions"}),f?e.jsx("div",{className:"border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-500",children:"Loading versions..."}):e.jsxs("select",{value:S,onChange:p=>v(p.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select a version..."}),Object.entries(y).filter(([p])=>{var k;return p!==((k=a==null?void 0:a.pluginData)==null?void 0:k.version)}).sort((p,k)=>{const C=O=>O.split(".").map(Number),[L,E]=[C(p[0]),C(k[0])];for(let O=0;O<Math.max(L.length,E.length);O++){const B=(E[O]||0)-(L[O]||0);if(B!==0)return B}return 0}).slice(0,15).map(([p])=>e.jsxs("option",{value:p,children:["v",p]},p))]}),e.jsx("button",{onClick:p=>{p.preventDefault(),S&&(y!=null&&y[S])&&(R(!0),window.open(y[S],"_blank"),setTimeout(()=>R(!1),2e3))},disabled:!S||j||f,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:j?e.jsx(e.Fragment,{children:e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"})}):e.jsx(e.Fragment,{children:e.jsx(me,{className:"h-4 w-4"})})})]})})]}),A("donate_link")&&e.jsxs("button",{onClick:()=>window.open(A("donate_link"),"_blank"),className:"w-full bg-red-100 hover:bg-red-200 text-red-700 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[e.jsx(Ys,{className:"h-4 w-4"}),e.jsx("span",{children:"Donate"})]})]}),A("author")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Author"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(De,{className:"h-8 w-8 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:A("author").replace(/<[^>]*>/g,"")}),A("author_profile")&&e.jsx("a",{href:A("author_profile"),target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800",children:"View Profile"})]})]})]}),A("tags")&&Object.keys(A("tags")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.keys(A("tags")).slice(0,10).map(p=>e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:p},p))})]}),A("contributors")&&Object.keys(A("contributors")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Contributors"}),e.jsx("div",{className:"flex flex-wrap gap-4",children:Object.entries(A("contributors")).slice(0,10).map(([p,k])=>e.jsx("a",{href:k.profile,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 rounded-lg shadow-sm",children:e.jsx("img",{src:k.avatar,alt:k.display_name||p,title:k.display_name||p,className:"w-8 h-8 rounded-full"})},p))})]})]})]})})]})};function Tt(){return e.jsx(it,{children:e.jsx(lt,{children:e.jsx(Ps,{children:e.jsxs(As,{children:[e.jsx(ie,{path:"/login",element:e.jsx(ht,{})}),e.jsxs(ie,{path:"/*",element:e.jsx(ct,{children:e.jsx(xt,{})}),children:[e.jsx(ie,{path:"dashboard",element:e.jsx(yt,{})})," ",e.jsx(ie,{path:"plugin-rank",element:e.jsx(bt,{})}),e.jsx(ie,{path:"keyword-analysis",element:e.jsx(wt,{})}),e.jsx(ie,{path:"analytics",element:e.jsx(Nt,{})}),e.jsx(ie,{path:"users",element:e.jsx(St,{})}),e.jsx(ie,{path:"settings",element:e.jsx(Pt,{})}),e.jsx(ie,{path:"profile",element:e.jsx(At,{})}),e.jsx(ie,{path:"plugin-details/:slug",element:e.jsx($t,{})}),e.jsx(ie,{path:"",element:e.jsx(os,{to:"/dashboard",replace:!0})})]})]})})})})}fs(document.getElementById("root")).render(e.jsx(r.StrictMode,{children:e.jsx(Tt,{})}));
