import { useState, useEffect } from "react";
import {
  Search,
  Plus,
  Refresh<PERSON><PERSON>,
  Trash2,
  <PERSON>,
  T<PERSON>dingUp,
  <PERSON><PERSON>hart,
  ExternalLink,
  Star,
  ScanSearch,
} from "lucide-react";
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LabelList,
} from "recharts";
import Modal from "../components/Modal";

// Utility function to decode HTML entities
const decodeHtmlEntities = (text) => {
  const textarea = document.createElement("textarea");
  textarea.innerHTML = text;
  return textarea.value;
};

// Utility function to shorten plugin names
const shortenPluginName = (name, maxLength = 40) => {
  const decodedName = decodeHtmlEntities(name);
  if (decodedName.length <= maxLength) return decodedName;

  // Try to find a good breaking point
  const words = decodedName.split(" ");
  let shortened = words[0];

  for (let i = 1; i < words.length; i++) {
    if ((shortened + " " + words[i]).length <= maxLength - 3) {
      shortened += " " + words[i];
    } else {
      break;
    }
  }

  return shortened + "...";
};

const KeywordAnalysis = () => {
  const [activeTab, setActiveTab] = useState("performance");
  const [addedPlugins, setAddedPlugins] = useState([]);
  const [selectedPlugin, setSelectedPlugin] = useState("");
  const [keywords, setKeywords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newKeyword, setNewKeyword] = useState("");
  const [selectedPluginForKeyword, setSelectedPluginForKeyword] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [selectedKeywords, setSelectedKeywords] = useState(new Set());
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedForAction, setSelectedForAction] = useState(new Set()); // Track which keywords have "Select" activated
  const [keywordToDelete, setKeywordToDelete] = useState(null); // Track single keyword for deletion
  const [showSingleDeleteModal, setShowSingleDeleteModal] = useState(false);
  const [competitors, setCompetitors] = useState([]);
  const [loadingCompetitors, setLoadingCompetitors] = useState(false);
  const [showAddCompetitorModal, setShowAddCompetitorModal] = useState(false);
  const [newCompetitorSlug, setNewCompetitorSlug] = useState("");
  const [keywordOccurrences, setKeywordOccurrences] = useState({});
  const [showLineChartModal, setShowLineChartModal] = useState(false);
  // Removed pagination-related state variables
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [selectedKeywordForAnalytics, setSelectedKeywordForAnalytics] =
    useState(null);
  const [keywordRankHistory, setKeywordRankHistory] = useState([]);
  const [relatedPlugins, setRelatedPlugins] = useState([]);
  const [loadingRankHistory, setLoadingRankHistory] = useState(false);
  const [loadingRelatedPlugins, setLoadingRelatedPlugins] = useState(false);

  // console.log("Related plugins: ", relatedPlugins);

  // Load all plugins from plugininformations collection
  const loadAddedPlugins = async () => {
    try {
      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(
        `${BASE_URL}/api/plugins/rank/all?limit=1000`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();
      if (data.success) {
        setAddedPlugins(data.plugins);
        console.log(
          `✅ Loaded ${
            data.plugins?.length || 0
          } plugins from plugininformations collection for keyword analysis`
        );
      } else {
        console.error("Failed to load plugins:", data.message);
      }
    } catch (error) {
      console.error(
        "Error loading plugins from plugininformations collection:",
        error
      );
      window.toast("Failed to load plugins from database", "error");
    }
  };

  // Load keywords for selected plugin (only load if plugin is selected)
  const loadKeywords = async () => {
    try {
      setLoading(true);

      // Only load keywords if a plugin is selected
      if (!selectedPlugin) {
        setKeywords([]);
        setKeywordOccurrences({});
        setLoading(false);
        return;
      }

      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";

      // Build URL with pluginSlug parameter
      const url = `${BASE_URL}/api/keywords?pluginSlug=${selectedPlugin}`;

      // console.log("Loading keywords from URL:", url);
      // console.log("Selected plugin:", selectedPlugin);

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      // console.log("Keywords API response:", data);

      if (data.success) {
        // console.log("Keywords loaded:", data.keywords.length);
        // Keywords now come with rank data and occurrences already included
        const processedKeywords = data.keywords.map((keyword) => ({
          ...keyword,
          position: keyword.latestRank,
          lastChecked: keyword.lastChecked || keyword.updatedAt,
        }));

        // console.log("Processed keywords:", processedKeywords);
        setKeywords(processedKeywords);

        // Create occurrences object from the keyword data (no need to fetch separately)
        const occurrencesData = {};
        processedKeywords.forEach((keyword) => {
          occurrencesData[keyword._id] = keyword.occurrences || 0;
        });
        setKeywordOccurrences(occurrencesData);

        // Reset displayed keywords count when keywords change
        setDisplayedKeywords(10);
      } else {
        console.error("Failed to load keywords:", data.message);
        window.toast(data.message || "Failed to load keywords", "error");
        setKeywords([]); // Clear keywords on error
        setKeywordOccurrences({});
      }
    } catch (error) {
      console.error("Error loading keywords:", error);
      window.toast("Failed to load keywords", "error");
      setKeywords([]); // Clear keywords on error
      setKeywordOccurrences({});
    } finally {
      setLoading(false);
    }
  };

  // Add new keyword
  const handleAddKeyword = async () => {
    if (!selectedPluginForKeyword || !newKeyword.trim()) {
      window.toast("Please select a plugin and enter a keyword", "error");
      return;
    }

    try {
      const token = localStorage.getItem("adminToken");
      const selectedPluginData = addedPlugins.find(
        (p) => p.pluginSlug === selectedPluginForKeyword
      );

      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(`${BASE_URL}/api/keywords`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          pluginSlug: selectedPluginForKeyword,
          pluginName:
            selectedPluginData?.displayName || selectedPluginForKeyword,
          keyword: newKeyword.trim(),
        }),
      });

      const data = await response.json();
      if (data.success) {
        window.toast("Keyword added successfully", "success");
        setNewKeyword("");
        setSelectedPluginForKeyword("");
        setShowAddModal(false);

        // Reload keywords if the added keyword is for the currently selected plugin
        if (selectedPluginForKeyword === selectedPlugin) {
          loadKeywords();
        }
      } else {
        window.toast(data.message || "Failed to add keyword", "error");
      }
    } catch (error) {
      console.error("Error adding keyword:", error);
      window.toast("Failed to add keyword", "error");
    }
  };

  // Refresh all keyword ranks
  const handleRefreshRanks = async () => {
    try {
      setRefreshing(true);
      window.toast("Refreshing keyword ranks...", "info");

      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(`${BASE_URL}/api/keywords/refresh-ranks`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      if (data.success) {
        window.toast(data.message, "success");
        // Reload keywords to show updated data
        await loadKeywords();
      } else {
        window.toast(
          data.message || "Failed to refresh keyword ranks",
          "error"
        );
      }
    } catch (error) {
      console.error("Error refreshing keyword ranks:", error);
      window.toast("Failed to refresh keyword ranks", "error");
    } finally {
      setRefreshing(false);
    }
  };

  // Bulk delete keywords
  const handleBulkDelete = async () => {
    try {
      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";

      const keywordIds = Array.from(selectedKeywords);
      const response = await fetch(`${BASE_URL}/api/keywords/bulk-delete`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ keywordIds }),
      });

      const data = await response.json();
      if (data.success) {
        window.toast(
          `${keywordIds.length} keywords deleted successfully`,
          "success"
        );
        setSelectedKeywords(new Set());
        setShowDeleteModal(false);
        loadKeywords();
      } else {
        window.toast(data.message || "Failed to delete keywords", "error");
      }
    } catch (error) {
      console.error("Error deleting keywords:", error);
      window.toast("Failed to delete keywords", "error");
    }
  };

  // Confirm single keyword deletion
  const confirmDeleteSingleKeyword = async () => {
    if (!keywordToDelete) return;

    try {
      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";

      const response = await fetch(
        `${BASE_URL}/api/keywords/${keywordToDelete}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();
      if (data.success) {
        window.toast("Keyword deleted successfully", "success");
        setShowSingleDeleteModal(false);
        setKeywordToDelete(null);
        // Remove from selectedForAction if it was selected
        const newSelected = new Set(selectedForAction);
        newSelected.delete(keywordToDelete);
        setSelectedForAction(newSelected);
        loadKeywords();
      } else {
        window.toast(data.message || "Failed to delete keyword", "error");
      }
    } catch (error) {
      console.error("Error deleting keyword:", error);
      window.toast("Failed to delete keyword", "error");
    }
  };

  // Load competitors
  const loadCompetitors = async (autoDiscover = false) => {
    try {
      setLoadingCompetitors(true);
      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const url = `${BASE_URL}/api/competitors${
        autoDiscover ? "?autoDiscover=true" : ""
      }`;
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      if (data.success) {
        setCompetitors(data.competitors);
        if (autoDiscover && data.competitors.length > 0) {
          window.toast(
            `Discovered ${data.competitors.length} competitor plugins`,
            "success"
          );
        }
      }
    } catch (error) {
      console.error("Error loading competitors:", error);
      window.toast("Failed to load competitors", "error");
    } finally {
      setLoadingCompetitors(false);
    }
  };

  // Add competitor
  const handleAddCompetitor = async () => {
    if (!newCompetitorSlug.trim()) {
      window.toast("Please enter a plugin slug", "error");
      return;
    }

    try {
      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(`${BASE_URL}/api/competitors`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          pluginSlug: newCompetitorSlug.trim(),
        }),
      });

      const data = await response.json();
      if (data.success) {
        window.toast("Competitor added successfully", "success");
        setNewCompetitorSlug("");
        setShowAddCompetitorModal(false);
        loadCompetitors();
      } else {
        window.toast(data.message || "Failed to add competitor", "error");
      }
    } catch (error) {
      console.error("Error adding competitor:", error);
      window.toast("Failed to add competitor", "error");
    }
  };

  // Handle line chart modal
  const handleShowLineChart = async (keyword) => {
    setSelectedKeywordForAnalytics(keyword);
    setShowLineChartModal(true);
    setLoadingRankHistory(true);

    try {
      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(
        `${BASE_URL}/api/keywords/ranks/${encodeURIComponent(
          keyword.keyword
        )}/${keyword.pluginSlug}?limit=30`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();
      if (data.success) {
        setKeywordRankHistory(data.rankHistory);
      } else {
        window.toast("Failed to load rank history", "error");
        setKeywordRankHistory([]);
      }
    } catch (error) {
      console.error("Error loading rank history:", error);
      window.toast("Failed to load rank history", "error");
      setKeywordRankHistory([]);
    } finally {
      setLoadingRankHistory(false);
    }
  };

  // Handle search modal
  const handleShowSearch = async (keyword) => {
    setSelectedKeywordForAnalytics(keyword);
    setShowSearchModal(true);
    setLoadingRelatedPlugins(true);

    try {
      // Fetch directly from WordPress API
      const searchKeyword = encodeURIComponent(keyword.keyword);
      const wpApiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${searchKeyword}&request[per_page]=50&request[fields][active_installs]=true&request[fields][ratings]=true&request[fields][tested]=true&request[fields][last_updated]=true`;

      const response = await fetch(wpApiUrl);
      const data = await response.json();

      if (data && data.plugins) {
        // Filter out the current plugin being analyzed and sort by active installs
        const filteredPlugins = data.plugins
          .filter((plugin) => plugin.slug !== keyword.pluginSlug)
          .sort((a, b) => (b.active_installs || 0) - (a.active_installs || 0))
          .slice(0, 10) // Get top 10
          .map((plugin) => ({
            pluginName: plugin.name,
            pluginSlug: plugin.slug,
            activeInstalls: plugin.active_installs || 0,
            rating: plugin.rating || 0,
            numRatings: plugin.num_ratings || 0,
            testedUpTo: plugin.tested || "N/A",
            lastUpdated: plugin.last_updated || "N/A",
            wordpressUrl: `https://wordpress.org/plugins/${plugin.slug}/`,
          }));

        setRelatedPlugins(filteredPlugins);
      } else {
        window.toast("Failed to load related plugins", "error");
        setRelatedPlugins([]);
      }
    } catch (error) {
      console.error("Error loading related plugins:", error);
      window.toast("Failed to load related plugins", "error");
      setRelatedPlugins([]);
    } finally {
      setLoadingRelatedPlugins(false);
    }
  };

  useEffect(() => {
    loadAddedPlugins();
  }, []);

  useEffect(() => {
    loadKeywords();
  }, [selectedPlugin]);

  // Removed infinity scroll functionality - now using simple overflow scroll

  useEffect(() => {
    if (activeTab === "competitors") {
      loadCompetitors(true); // Auto-discover competitors on first load
    }
  }, [activeTab]);

  return (
    <div className="space-y-6">
      {/* Header with Tabs */}
      <div className="p-4">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Search className="h-6 w-6 text-blue-600 mr-2" />
              Keyword Analysis
            </h1>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("performance")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "performance"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <TrendingUp className="h-4 w-4 inline mr-2" />
              Keyword Performance
            </button>
            <button
              onClick={() => setActiveTab("competitors")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "competitors"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Users className="h-4 w-4 inline mr-2" />
              Competitors
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === "performance" && (
        <>
          {/* Filters - Optimized Layout */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex justify-between items-stretch md:items-center gap-3">
              {/* Filter by Plugin */}
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                  Plugin:
                </label>
                <select
                  value={selectedPlugin}
                  onChange={(e) => setSelectedPlugin(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All plugins</option>
                  {addedPlugins.map((plugin) => (
                    <option key={plugin.pluginSlug} value={plugin.pluginSlug}>
                      {plugin.displayName}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center gap-2">
                {selectedKeywords.size > 0 && (
                  <button
                    onClick={() => setShowDeleteModal(true)}
                    className="flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete ({selectedKeywords.size})
                  </button>
                )}
                <button
                  onClick={() => setShowAddModal(true)}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </button>
                <button
                  onClick={handleRefreshRanks}
                  disabled={refreshing}
                  className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm"
                >
                  <RefreshCw
                    className={`h-4 w-4 mr-1 ${
                      refreshing ? "animate-spin" : ""
                    }`}
                  />
                  {refreshing ? "Refreshing..." : "Refresh Ranks"}
                </button>
              </div>
            </div>
          </div>

          {/* Keywords Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-base font-semibold text-gray-900">
                Keywords
                {selectedPlugin && (
                  <span className="text-sm font-normal text-gray-500 ml-2">
                    for{" "}
                    {
                      addedPlugins.find((p) => p.pluginSlug === selectedPlugin)
                        ?.displayName
                    }
                  </span>
                )}
              </h3>

              {/* Keywords info */}
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-700">
                  Showing {Math.min(displayedKeywords, keywords.length)} of{" "}
                  {keywords.length} keywords
                </div>
                {displayedKeywords < keywords.length && (
                  <div className="text-sm text-blue-600">
                    Scroll down to load more...
                  </div>
                )}
              </div>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-3" />
                <p className="text-gray-600">Loading keywords...</p>
              </div>
            ) : keywords.length > 0 ? (
              <>
                {/* Keywords info */}
                {/* <div className="flex justify-between items-center mb-4">
                  <div className="text-sm text-gray-700">
                    Showing {Math.min(displayedKeywords, keywords.length)} of{" "}
                    {keywords.length} keywords
                  </div>
                  {displayedKeywords < keywords.length && (
                    <div className="text-sm text-blue-600">
                      Scroll down to load more...
                    </div>
                  )}
                </div> */}
                <div className="overflow-x-auto max-h-[470px] overflow-y-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50 sticky top-0 z-10">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                          <input
                            type="checkbox"
                            checked={
                              keywords.every((k) =>
                                selectedKeywords.has(k._id)
                              ) && keywords.length > 0
                            }
                            onChange={(e) => {
                              const newSelected = new Set(selectedKeywords);
                              if (e.target.checked) {
                                keywords.forEach((k) => newSelected.add(k._id));
                              } else {
                                keywords.forEach((k) =>
                                  newSelected.delete(k._id)
                                );
                              }
                              setSelectedKeywords(newSelected);
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">
                          Keyword
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Position
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                          Analytics
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                          Occurrences
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Tracked
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Updated
                        </th>
                        {/* <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th> */}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {keywords.map((keyword, index) => (
                        <tr
                          key={keyword._id}
                          className={
                            index % 2 === 0 ? "bg-white" : "bg-gray-50"
                          }
                        >
                          <td className="px-4 py-3 whitespace-nowrap w-12">
                            <input
                              type="checkbox"
                              checked={selectedKeywords.has(keyword._id)}
                              onChange={(e) => {
                                const newSelected = new Set(selectedKeywords);
                                if (e.target.checked) {
                                  newSelected.add(keyword._id);
                                } else {
                                  newSelected.delete(keyword._id);
                                }
                                setSelectedKeywords(newSelected);
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap w-1/4">
                            <div className="flex items-center space-x-2">
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {keyword.keyword}
                                </div>
                                {/* <div className="text-xs text-gray-500">
                                {keyword.pluginName || keyword.pluginSlug}
                              </div> */}
                              </div>
                              {keyword.source && (
                                <span
                                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    keyword.source === "manual"
                                      ? "bg-blue-100 text-blue-800"
                                      : "bg-green-100 text-green-800"
                                  }`}
                                >
                                  {keyword.source}
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6">
                            <div className="flex items-center space-x-2">
                              <span>{keyword.position || "-"}</span>
                              {keyword.rankChange !== null &&
                                keyword.rankChange !== undefined && (
                                  <span
                                    className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
                                      keyword.rankChange < 0
                                        ? "bg-green-100 text-green-800"
                                        : keyword.rankChange > 0
                                        ? "bg-red-100 text-red-800"
                                        : "bg-gray-100 text-gray-800"
                                    }`}
                                  >
                                    {keyword.rankChange < 0
                                      ? "↑"
                                      : keyword.rankChange > 0
                                      ? "↓"
                                      : "="}
                                    {Math.abs(keyword.rankChange)}
                                  </span>
                                )}
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm w-20">
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleShowLineChart(keyword)}
                                className="inline-flex items-center p-1.5 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors"
                                title="View Rank Analytics"
                              >
                                <TrendingUp className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleShowSearch(keyword)}
                                className="inline-flex items-center p-1.5 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors"
                                title="Search Related Plugins"
                              >
                                <ScanSearch className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-20">
                            {keywordOccurrences[keyword._id] || 0}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6">
                            {keyword.addedAt
                              ? new Date(keyword.addedAt).toLocaleDateString(
                                  "en-GB"
                                )
                              : "-"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6">
                            {keyword.updatedAt
                              ? new Date(keyword.updatedAt).toLocaleDateString(
                                  "en-GB"
                                )
                              : "-"}
                          </td>
                          {/* <td className="px-4 py-3 whitespace-nowrap text-sm">
                          {selectedForAction.has(keyword._id) ? (
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() =>
                                  handleDeleteSingleKeyword(keyword._id)
                                }
                                className="inline-flex items-center px-3 py-1 bg-red-600 text-white text-xs rounded-md hover:bg-red-700 transition-colors"
                              >
                                <Trash2 className="h-3 w-3 mr-1" />
                                Delete
                              </button>
                              <button
                                onClick={() => {
                                  const newSelected = new Set(
                                    selectedForAction
                                  );
                                  newSelected.delete(keyword._id);
                                  setSelectedForAction(newSelected);
                                }}
                                className="inline-flex items-center px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded-md hover:bg-gray-400 transition-colors"
                              >
                                Cancel
                              </button>
                            </div>
                          ) : (
                            <button
                              onClick={() => {
                                const newSelected = new Set(selectedForAction);
                                newSelected.add(keyword._id);
                                setSelectedForAction(newSelected);
                              }}
                              className="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors"
                            >
                              Select
                            </button>
                          )}
                        </td> */}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div className="p-8 text-center">
                <Search className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  No Keywords Found
                </h4>
                <p className="text-gray-600 mb-4">
                  {selectedPlugin
                    ? "No keywords added for this plugin yet."
                    : "Please select a plugin first to view and manage keywords."}
                </p>
                {selectedPlugin && (
                  <button
                    onClick={() => setShowAddModal(true)}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Keyword
                  </button>
                )}
              </div>
            )}
          </div>
        </>
      )}

      {activeTab === "competitors" && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-base font-semibold text-gray-900">
              Competitor Plugins
            </h3>
            <div className="flex items-center gap-2">
              <button
                onClick={() => loadCompetitors(true)}
                disabled={loadingCompetitors}
                className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm"
              >
                <RefreshCw
                  className={`h-4 w-4 mr-1 ${
                    loadingCompetitors ? "animate-spin" : ""
                  }`}
                />
                {loadingCompetitors ? "Discovering..." : "Discover"}
              </button>
              <button
                onClick={() => setShowAddCompetitorModal(true)}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Manually
              </button>
            </div>
          </div>

          {loadingCompetitors ? (
            <div className="p-8 text-center">
              <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-3" />
              <p className="text-gray-600">Loading competitors...</p>
            </div>
          ) : competitors.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plugin Name
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Slug
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Current Rank
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Active Installs
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tags
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Added Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {competitors.map((competitor, index) => (
                    <tr
                      key={competitor._id}
                      className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                    >
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {competitor.pluginName}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-xs text-gray-500 font-mono">
                          {competitor.pluginSlug}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                        {competitor.currentRank
                          ? `#${competitor.currentRank}`
                          : "N/A"}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                        {competitor.activeInstalls?.toLocaleString() || "N/A"}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                        <div className="flex flex-wrap gap-1">
                          {competitor.tags && competitor.tags.length > 0 ? (
                            competitor.tags.slice(0, 3).map((tag, tagIndex) => (
                              <span
                                key={`${competitor._id}-tag-${tagIndex}`}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                              >
                                {tag}
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-400">No tags</span>
                          )}
                          {competitor.tags && competitor.tags.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{competitor.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                        {new Date(competitor.createdAt).toLocaleDateString(
                          "en-GB"
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="p-8 text-center">
              <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                No Competitors Found
              </h4>
              <p className="text-gray-600 mb-4">
                Add keywords to automatically discover competitor plugins, or
                add competitors manually.
              </p>
              <button
                onClick={() => setShowAddCompetitorModal(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add First Competitor
              </button>
            </div>
          )}
        </div>
      )}

      {/* Add Keyword Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => {
          setShowAddModal(false);
          setNewKeyword("");
          setSelectedPluginForKeyword("");
        }}
        title="Add New Keyword"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Plugin
            </label>
            <select
              value={selectedPluginForKeyword}
              onChange={(e) => setSelectedPluginForKeyword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Choose a plugin...</option>
              {addedPlugins.map((plugin) => (
                <option key={plugin.pluginSlug} value={plugin.pluginSlug}>
                  {plugin.displayName}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Keyword
            </label>
            <input
              type="text"
              value={newKeyword}
              onChange={(e) => setNewKeyword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter keyword..."
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleAddKeyword();
                }
              }}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => {
                setShowAddModal(false);
                setNewKeyword("");
                setSelectedPluginForKeyword("");
              }}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleAddKeyword}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Keyword
            </button>
          </div>
        </div>
      </Modal>

      {/* Bulk Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Keywords"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to delete {selectedKeywords.size} selected
            keyword(s)? This action cannot be undone and will also remove all
            related analytics data.
          </p>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => setShowDeleteModal(false)}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleBulkDelete}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Delete Keywords
            </button>
          </div>
        </div>
      </Modal>

      {/* Single Delete Confirmation Modal */}
      <Modal
        isOpen={showSingleDeleteModal}
        onClose={() => {
          setShowSingleDeleteModal(false);
          setKeywordToDelete(null);
        }}
        title="Delete Keyword"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to delete this keyword? This action cannot be
            undone and will also remove all related analytics data.
          </p>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => {
                setShowSingleDeleteModal(false);
                setKeywordToDelete(null);
              }}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={confirmDeleteSingleKeyword}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Delete Keyword
            </button>
          </div>
        </div>
      </Modal>

      {/* Add Competitor Modal */}
      <Modal
        isOpen={showAddCompetitorModal}
        onClose={() => {
          setShowAddCompetitorModal(false);
          setNewCompetitorSlug("");
        }}
        title="Add Competitor Plugin"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Plugin Slug
            </label>
            <input
              type="text"
              value={newCompetitorSlug}
              onChange={(e) => setNewCompetitorSlug(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter plugin slug..."
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleAddCompetitor();
                }
              }}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => {
                setShowAddCompetitorModal(false);
                setNewCompetitorSlug("");
              }}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleAddCompetitor}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Competitor
            </button>
          </div>
        </div>
      </Modal>

      {/* Line Chart Modal */}
      <Modal
        isOpen={showLineChartModal}
        onClose={() => {
          setShowLineChartModal(false);
          setSelectedKeywordForAnalytics(null);
          setKeywordRankHistory([]);
        }}
        title={`Rank Analytics - ${selectedKeywordForAnalytics?.keyword || ""}`}
      >
        <div className="space-y-4">
          {loadingRankHistory ? (
            <div className="p-8 text-center">
              <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-3" />
              <p className="text-gray-600">Loading rank history...</p>
            </div>
          ) : keywordRankHistory.length > 0 ? (
            <div className="space-y-4">
              {/* Line Chart - Full Width */}
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-sm font-medium text-gray-900">
                    Rank Trend (Last 30 days)
                  </h4>
                  <p className="text-sm text-gray-600">
                    <strong>Plugin:</strong>{" "}
                    {selectedKeywordForAnalytics?.pluginName}
                  </p>
                </div>

                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart
                      data={keywordRankHistory
                        .slice()
                        .reverse()
                        .map((entry) => ({
                          ...entry,
                          displayDate: entry.date,
                          invertedRank: entry.rank ? -entry.rank : 0, // Invert for better visualization (lower rank = higher on chart)
                        }))}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="displayDate"
                        tick={{ fontSize: 12 }}
                        tickLine={{ stroke: "#d1d5db" }}
                        axisLine={{ stroke: "#d1d5db" }}
                      />
                      <YAxis
                        domain={["dataMin", "dataMax"]}
                        tick={{ fontSize: 12 }}
                        tickLine={{ stroke: "#d1d5db" }}
                        axisLine={{ stroke: "#d1d5db" }}
                        tickFormatter={(value) => `#${Math.abs(value)}`}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: "#ffffff",
                          border: "1px solid #d1d5db",
                          borderRadius: "6px",
                          fontSize: "12px",
                        }}
                        formatter={(value) => [`#${Math.abs(value)}`, "Rank"]}
                        labelFormatter={(label) => `Date: ${label}`}
                      />
                      <Line
                        type="monotone"
                        dataKey="invertedRank"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                        activeDot={{
                          r: 6,
                          stroke: "#3b82f6",
                          strokeWidth: 2,
                        }}
                      >
                        <LabelList
                          dataKey="invertedRank"
                          position="top"
                          formatter={(value) => `#${Math.abs(value)}`}
                          style={{
                            fontSize: "12px",
                            fill: "#374151",
                            fontWeight: "500",
                          }}
                        />
                      </Line>
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Summary Stats */}
              {/* <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-xs text-gray-500 mb-1">Best Rank</div>
                  <div className="text-lg font-semibold text-green-600">
                    #{Math.min(...keywordRankHistory.map((h) => h.rank))}
                  </div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-xs text-gray-500 mb-1">Current Rank</div>
                  <div className="text-lg font-semibold text-blue-600">
                    #{selectedKeywordForAnalytics?.position || "N/A"}
                  </div>
                </div>
              </div> */}

              {/* Plugin Info */}
              {/* <div className="text-xs text-gray-500 bg-gray-50 rounded-lg p-3">
                <p className="mb-1">
                  <strong>Plugin:</strong>{" "}
                  {selectedKeywordForAnalytics?.pluginName}
                </p>
                <p className="mb-1">
                  <strong>Keyword:</strong>{" "}
                  {selectedKeywordForAnalytics?.keyword}
                </p>
                <p>
                  <strong>Data Points:</strong> {keywordRankHistory.length}{" "}
                  entries
                </p>
              </div> */}
            </div>
          ) : (
            <div className="p-8 text-center">
              <LineChart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                No Rank History
              </h4>
              <p className="text-gray-600">
                No rank history found for this keyword.
              </p>
            </div>
          )}
          <div className="flex justify-end pt-4">
            <button
              onClick={() => {
                setShowLineChartModal(false);
                setSelectedKeywordForAnalytics(null);
                setKeywordRankHistory([]);
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </Modal>

      {/* Search Related Plugins Modal */}
      <Modal
        isOpen={showSearchModal}
        onClose={() => {
          setShowSearchModal(false);
          setSelectedKeywordForAnalytics(null);
          setRelatedPlugins([]);
        }}
        title={`Related Plugins - "${
          selectedKeywordForAnalytics?.keyword || ""
        }"`}
        maxWidth="max-w-6xl"
        fixedHeight={true}
      >
        <div className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto">
            {loadingRelatedPlugins ? (
              <div className="p-8 text-center">
                <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-3" />
                <p className="text-gray-600">Loading related plugins...</p>
              </div>
            ) : relatedPlugins.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Plugin Name
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Active Installs
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Rating
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tested Up To
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Update
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {relatedPlugins.map((plugin, index) => (
                      <tr
                        key={plugin.pluginSlug}
                        className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                      >
                        <td className="px-4 py-3 whitespace-nowrap">
                          <a
                            href={plugin.wordpressUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 font-medium flex items-center"
                            title={decodeHtmlEntities(plugin.pluginName)}
                          >
                            {shortenPluginName(plugin.pluginName)}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </a>
                          <div className="text-xs text-gray-500 font-mono">
                            {plugin.pluginSlug}
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                          {plugin.activeInstalls > 0
                            ? plugin.activeInstalls.toLocaleString()
                            : "N/A"}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 mr-1" />
                            <span>
                              {plugin.rating > 0
                                ? plugin.rating.toFixed(1)
                                : "N/A"}
                              {plugin.numRatings > 0 && (
                                <span className="text-xs text-gray-400 ml-1">
                                  ({plugin.numRatings})
                                </span>
                              )}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                          {plugin.testedUpTo !== "N/A"
                            ? `WP ${plugin.testedUpTo}`
                            : "N/A"}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                          {plugin.lastUpdated !== "N/A"
                            ? (() => {
                                const date = new Date(plugin.lastUpdated);
                                // Check if date is valid
                                if (isNaN(date.getTime())) {
                                  return "N/A";
                                }
                                const day = date
                                  .getDate()
                                  .toString()
                                  .padStart(2, "0");
                                const month = (date.getMonth() + 1)
                                  .toString()
                                  .padStart(2, "0");
                                const year = date.getFullYear();
                                return `${month}-${day}-${year}`;
                              })()
                            : "N/A"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="p-8 text-center">
                <Search className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  No Related Plugins Found
                </h4>
                <p className="text-gray-600">
                  No plugins found containing this keyword.
                </p>
              </div>
            )}
          </div>
          <div className="flex justify-end pt-4 border-t border-gray-200 flex-shrink-0">
            <button
              onClick={() => {
                setShowSearchModal(false);
                setSelectedKeywordForAnalytics(null);
                setRelatedPlugins([]);
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default KeywordAnalysis;
